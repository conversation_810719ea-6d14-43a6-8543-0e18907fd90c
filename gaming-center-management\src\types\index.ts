// أنواع البيانات الأساسية لنظام إدارة الأجهزة

export interface Device {
  id: string;
  name: string;
  type: 'PS4' | 'PS5' | 'Xbox' | 'PC' | 'Nintendo';
  status: 'available' | 'occupied' | 'paused' | 'maintenance';
  currentSession?: Session;
  pricePerHour: number;
  location?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Customer {
  id: string;
  name: string;
  phone?: string;
  email?: string;
  totalPlayTime: number; // بالدقائق
  totalSpent: number;
  membershipLevel: 'regular' | 'silver' | 'gold' | 'platinum';
  createdAt: Date;
  lastVisit?: Date;
}

export interface Session {
  id: string;
  deviceId: string;
  customerId?: string;
  customerName: string;
  startTime: Date;
  endTime?: Date;
  plannedDuration?: number; // بالدقائق، null للوقت المفتوح
  actualDuration?: number; // بالدقائق
  gameType: 'single' | 'multiplayer';
  pricePerHour: number;
  totalCost: number;
  status: 'active' | 'paused' | 'completed' | 'cancelled';
  pausedTime?: number; // إجمالي الوقت المتوقف بالدقائق
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface PricingRule {
  id: string;
  name: string;
  gameType: 'single' | 'multiplayer' | 'both';
  deviceTypes: Device['type'][];
  pricePerHour: number;
  timeSlots?: TimeSlot[];
  isActive: boolean;
  priority: number; // أولوية التطبيق
  createdAt: Date;
  validFrom?: Date;
  validTo?: Date;
}

export interface TimeSlot {
  startTime: string; // "HH:MM" format
  endTime: string; // "HH:MM" format
  daysOfWeek: number[]; // 0-6 (الأحد-السبت)
  multiplier: number; // مضاعف السعر
}

export interface Offer {
  id: string;
  name: string;
  description: string;
  type: 'discount' | 'package' | 'happy_hour';
  discountPercentage?: number;
  fixedPrice?: number;
  minimumDuration?: number; // بالدقائق
  maximumDuration?: number; // بالدقائق
  applicableDevices: Device['type'][];
  validFrom: Date;
  validTo: Date;
  isActive: boolean;
  usageCount: number;
  maxUsage?: number;
  createdAt: Date;
}

export interface Notification {
  id: string;
  type: 'time_warning' | 'time_up' | 'payment_due' | 'device_available';
  title: string;
  message: string;
  sessionId?: string;
  deviceId?: string;
  isRead: boolean;
  playSound: boolean;
  createdAt: Date;
}

export interface DashboardStats {
  totalDevices: number;
  availableDevices: number;
  occupiedDevices: number;
  pausedDevices: number;
  maintenanceDevices: number;
  todayRevenue: number;
  todaySessions: number;
  averageSessionDuration: number;
  popularDeviceType: Device['type'];
}

export interface SystemSettings {
  warningTime: number; // دقائق قبل انتهاء الوقت للتنبيه
  soundEnabled: boolean;
  autoCalculatePricing: boolean;
  defaultSessionDuration: number; // دقائق
  currencySymbol: string;
  businessHours: {
    open: string; // "HH:MM"
    close: string; // "HH:MM"
  };
  timezone: string;
}

// أنواع الأحداث للنظام
export type SystemEvent = 
  | { type: 'SESSION_STARTED'; payload: Session }
  | { type: 'SESSION_ENDED'; payload: Session }
  | { type: 'SESSION_PAUSED'; payload: Session }
  | { type: 'SESSION_RESUMED'; payload: Session }
  | { type: 'DEVICE_STATUS_CHANGED'; payload: { deviceId: string; oldStatus: Device['status']; newStatus: Device['status'] } }
  | { type: 'TIME_WARNING'; payload: { sessionId: string; remainingMinutes: number } }
  | { type: 'TIME_UP'; payload: { sessionId: string } }
  | { type: 'PAYMENT_CALCULATED'; payload: { sessionId: string; amount: number } };

// أنواع الفلاتر والبحث
export interface DeviceFilter {
  status?: Device['status'][];
  type?: Device['type'][];
  searchTerm?: string;
}

export interface SessionFilter {
  status?: Session['status'][];
  gameType?: Session['gameType'][];
  dateFrom?: Date;
  dateTo?: Date;
  customerId?: string;
  deviceId?: string;
}

export interface CustomerFilter {
  membershipLevel?: Customer['membershipLevel'][];
  searchTerm?: string;
  lastVisitFrom?: Date;
  lastVisitTo?: Date;
}
