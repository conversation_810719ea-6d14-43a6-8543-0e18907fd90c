import { Session, SystemEvent, Notification } from '../types';
import { format, differenceInMinutes, addMinutes } from 'date-fns';

export class TimeManager {
  private timers: Map<string, NodeJS.Timeout> = new Map();
  private warningTimers: Map<string, NodeJS.Timeout> = new Map();
  private sessions: Map<string, Session> = new Map();
  private eventListeners: ((event: SystemEvent) => void)[] = [];
  private warningTime: number = 10; // دقائق قبل انتهاء الوقت للتنبيه

  constructor(warningTime: number = 10) {
    this.warningTime = warningTime;
  }

  // إضافة مستمع للأحداث
  addEventListener(listener: (event: SystemEvent) => void) {
    this.eventListeners.push(listener);
  }

  // إزالة مستمع الأحداث
  removeEventListener(listener: (event: SystemEvent) => void) {
    const index = this.eventListeners.indexOf(listener);
    if (index > -1) {
      this.eventListeners.splice(index, 1);
    }
  }

  // إرسال حدث للمستمعين
  private emitEvent(event: SystemEvent) {
    this.eventListeners.forEach(listener => listener(event));
  }

  // بدء جلسة جديدة
  startSession(session: Session): void {
    this.sessions.set(session.id, { ...session, status: 'active' });
    
    // إذا كان هناك وقت محدد، قم بإعداد المؤقتات
    if (session.plannedDuration) {
      this.setupTimers(session);
    }

    this.emitEvent({ type: 'SESSION_STARTED', payload: session });
  }

  // إيقاف الجلسة مؤقتاً
  pauseSession(sessionId: string): Session | null {
    const session = this.sessions.get(sessionId);
    if (!session || session.status !== 'active') {
      return null;
    }

    // حفظ الوقت المتوقف
    const now = new Date();
    const pausedTime = (session.pausedTime || 0) + differenceInMinutes(now, session.startTime);
    
    const updatedSession: Session = {
      ...session,
      status: 'paused',
      pausedTime,
      updatedAt: now
    };

    this.sessions.set(sessionId, updatedSession);
    
    // إلغاء المؤقتات
    this.clearTimers(sessionId);

    this.emitEvent({ type: 'SESSION_PAUSED', payload: updatedSession });
    return updatedSession;
  }

  // استئناف الجلسة
  resumeSession(sessionId: string): Session | null {
    const session = this.sessions.get(sessionId);
    if (!session || session.status !== 'paused') {
      return null;
    }

    const now = new Date();
    const updatedSession: Session = {
      ...session,
      status: 'active',
      startTime: now, // إعادة تعيين وقت البداية
      updatedAt: now
    };

    this.sessions.set(sessionId, updatedSession);

    // إعادة إعداد المؤقتات إذا كان هناك وقت محدد
    if (session.plannedDuration) {
      const remainingTime = session.plannedDuration - (session.pausedTime || 0);
      if (remainingTime > 0) {
        this.setupTimers({ ...updatedSession, plannedDuration: remainingTime });
      }
    }

    this.emitEvent({ type: 'SESSION_RESUMED', payload: updatedSession });
    return updatedSession;
  }

  // إنهاء الجلسة
  endSession(sessionId: string): Session | null {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return null;
    }

    const now = new Date();
    const actualDuration = this.calculateActualDuration(session, now);
    const totalCost = this.calculateTotalCost(session, actualDuration);

    const completedSession: Session = {
      ...session,
      status: 'completed',
      endTime: now,
      actualDuration,
      totalCost,
      updatedAt: now
    };

    this.sessions.set(sessionId, completedSession);
    this.clearTimers(sessionId);

    this.emitEvent({ type: 'SESSION_ENDED', payload: completedSession });
    this.emitEvent({ type: 'PAYMENT_CALCULATED', payload: { sessionId, amount: totalCost } });
    
    return completedSession;
  }

  // إلغاء الجلسة
  cancelSession(sessionId: string): Session | null {
    const session = this.sessions.get(sessionId);
    if (!session) {
      return null;
    }

    const now = new Date();
    const cancelledSession: Session = {
      ...session,
      status: 'cancelled',
      endTime: now,
      updatedAt: now
    };

    this.sessions.set(sessionId, cancelledSession);
    this.clearTimers(sessionId);

    return cancelledSession;
  }

  // الحصول على الجلسة
  getSession(sessionId: string): Session | null {
    return this.sessions.get(sessionId) || null;
  }

  // الحصول على جميع الجلسات النشطة
  getActiveSessions(): Session[] {
    return Array.from(this.sessions.values()).filter(s => s.status === 'active' || s.status === 'paused');
  }

  // حساب الوقت المتبقي للجلسة
  getRemainingTime(sessionId: string): number | null {
    const session = this.sessions.get(sessionId);
    if (!session || !session.plannedDuration || session.status !== 'active') {
      return null;
    }

    const now = new Date();
    const elapsedTime = differenceInMinutes(now, session.startTime);
    const totalPausedTime = session.pausedTime || 0;
    const effectiveElapsedTime = elapsedTime - totalPausedTime;
    
    return Math.max(0, session.plannedDuration - effectiveElapsedTime);
  }

  // حساب الوقت الفعلي للجلسة
  private calculateActualDuration(session: Session, endTime: Date): number {
    const totalTime = differenceInMinutes(endTime, session.startTime);
    const pausedTime = session.pausedTime || 0;
    return Math.max(0, totalTime - pausedTime);
  }

  // حساب التكلفة الإجمالية
  private calculateTotalCost(session: Session, actualDuration: number): number {
    const hours = actualDuration / 60;
    return Math.round(hours * session.pricePerHour * 100) / 100; // تقريب لأقرب قرش
  }

  // إعداد المؤقتات للجلسة
  private setupTimers(session: Session): void {
    if (!session.plannedDuration) return;

    const sessionDurationMs = session.plannedDuration * 60 * 1000;
    const warningTimeMs = Math.max(0, sessionDurationMs - (this.warningTime * 60 * 1000));

    // مؤقت التنبيه
    if (warningTimeMs > 0) {
      const warningTimer = setTimeout(() => {
        this.emitEvent({ 
          type: 'TIME_WARNING', 
          payload: { sessionId: session.id, remainingMinutes: this.warningTime } 
        });
        this.playWarningSound();
      }, warningTimeMs);

      this.warningTimers.set(session.id, warningTimer);
    }

    // مؤقت انتهاء الوقت
    const endTimer = setTimeout(() => {
      this.emitEvent({ type: 'TIME_UP', payload: { sessionId: session.id } });
      this.playTimeUpSound();
      this.endSession(session.id);
    }, sessionDurationMs);

    this.timers.set(session.id, endTimer);
  }

  // إلغاء المؤقتات
  private clearTimers(sessionId: string): void {
    const timer = this.timers.get(sessionId);
    const warningTimer = this.warningTimers.get(sessionId);

    if (timer) {
      clearTimeout(timer);
      this.timers.delete(sessionId);
    }

    if (warningTimer) {
      clearTimeout(warningTimer);
      this.warningTimers.delete(sessionId);
    }
  }

  // تشغيل صوت التنبيه
  private playWarningSound(): void {
    this.playSound(800, 200); // تردد 800Hz لمدة 200ms
  }

  // تشغيل صوت انتهاء الوقت
  private playTimeUpSound(): void {
    // تشغيل 3 نغمات متتالية
    this.playSound(1000, 300);
    setTimeout(() => this.playSound(1000, 300), 400);
    setTimeout(() => this.playSound(1000, 300), 800);
  }

  // تشغيل صوت بتردد ومدة محددة
  private playSound(frequency: number, duration: number): void {
    try {
      const audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.frequency.value = frequency;
      oscillator.type = 'sine';

      gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
      gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration / 1000);

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + duration / 1000);
    } catch (error) {
      console.warn('لا يمكن تشغيل الصوت:', error);
    }
  }

  // تنظيف جميع المؤقتات
  cleanup(): void {
    this.timers.forEach(timer => clearTimeout(timer));
    this.warningTimers.forEach(timer => clearTimeout(timer));
    this.timers.clear();
    this.warningTimers.clear();
    this.sessions.clear();
    this.eventListeners.length = 0;
  }
}
