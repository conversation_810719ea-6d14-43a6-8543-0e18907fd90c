import { Device, Session, PricingRule, Offer, TimeSlot } from '../types';
import { format, isWithinInterval, getDay, parse } from 'date-fns';

export class PricingManager {
  private pricingRules: PricingRule[] = [];
  private offers: Offer[] = [];

  constructor() {
    this.initializeDefaultPricing();
  }

  // إضافة قاعدة تسعير جديدة
  addPricingRule(rule: Omit<PricingRule, 'id' | 'createdAt'>): PricingRule {
    const newRule: PricingRule = {
      ...rule,
      id: this.generateId(),
      createdAt: new Date()
    };
    
    this.pricingRules.push(newRule);
    this.sortPricingRulesByPriority();
    return newRule;
  }

  // تحديث قاعدة تسعير
  updatePricingRule(id: string, updates: Partial<PricingRule>): PricingRule | null {
    const index = this.pricingRules.findIndex(rule => rule.id === id);
    if (index === -1) return null;

    this.pricingRules[index] = { ...this.pricingRules[index], ...updates };
    this.sortPricingRulesByPriority();
    return this.pricingRules[index];
  }

  // حذف قاعدة تسعير
  deletePricingRule(id: string): boolean {
    const index = this.pricingRules.findIndex(rule => rule.id === id);
    if (index === -1) return false;

    this.pricingRules.splice(index, 1);
    return true;
  }

  // إضافة عرض جديد
  addOffer(offer: Omit<Offer, 'id' | 'createdAt' | 'usageCount'>): Offer {
    const newOffer: Offer = {
      ...offer,
      id: this.generateId(),
      usageCount: 0,
      createdAt: new Date()
    };
    
    this.offers.push(newOffer);
    return newOffer;
  }

  // تحديث عرض
  updateOffer(id: string, updates: Partial<Offer>): Offer | null {
    const index = this.offers.findIndex(offer => offer.id === id);
    if (index === -1) return null;

    this.offers[index] = { ...this.offers[index], ...updates };
    return this.offers[index];
  }

  // حذف عرض
  deleteOffer(id: string): boolean {
    const index = this.offers.findIndex(offer => offer.id === id);
    if (index === -1) return false;

    this.offers.splice(index, 1);
    return true;
  }

  // حساب السعر للجلسة
  calculatePrice(device: Device, gameType: 'single' | 'multiplayer', startTime: Date, durationMinutes: number): {
    basePrice: number;
    finalPrice: number;
    appliedRule?: PricingRule;
    appliedOffer?: Offer;
    breakdown: PriceBreakdown[];
  } {
    // العثور على قاعدة التسعير المناسبة
    const applicableRule = this.findApplicablePricingRule(device, gameType, startTime);
    const baseHourlyRate = applicableRule?.pricePerHour || device.pricePerHour;

    // حساب السعر الأساسي
    const hours = durationMinutes / 60;
    let basePrice = baseHourlyRate * hours;

    // تطبيق مضاعفات الوقت إذا وجدت
    const breakdown: PriceBreakdown[] = [];
    if (applicableRule?.timeSlots) {
      basePrice = this.calculateTimeSlotPricing(baseHourlyRate, startTime, durationMinutes, applicableRule.timeSlots, breakdown);
    } else {
      breakdown.push({
        description: `${durationMinutes} دقيقة × ${baseHourlyRate} ريال/ساعة`,
        amount: basePrice,
        type: 'base'
      });
    }

    // البحث عن أفضل عرض متاح
    const applicableOffer = this.findBestOffer(device, gameType, startTime, durationMinutes);
    let finalPrice = basePrice;

    if (applicableOffer) {
      finalPrice = this.applyOffer(basePrice, applicableOffer, durationMinutes, breakdown);
    }

    return {
      basePrice: Math.round(basePrice * 100) / 100,
      finalPrice: Math.round(finalPrice * 100) / 100,
      appliedRule: applicableRule,
      appliedOffer: applicableOffer,
      breakdown
    };
  }

  // العثور على قاعدة التسعير المناسبة
  private findApplicablePricingRule(device: Device, gameType: 'single' | 'multiplayer', startTime: Date): PricingRule | undefined {
    const now = new Date();
    
    return this.pricingRules
      .filter(rule => 
        rule.isActive &&
        (rule.gameType === 'both' || rule.gameType === gameType) &&
        rule.deviceTypes.includes(device.type) &&
        (!rule.validFrom || rule.validFrom <= now) &&
        (!rule.validTo || rule.validTo >= now) &&
        this.isTimeSlotApplicable(rule.timeSlots, startTime)
      )
      .sort((a, b) => b.priority - a.priority)[0];
  }

  // التحقق من انطباق الفترة الزمنية
  private isTimeSlotApplicable(timeSlots: TimeSlot[] | undefined, startTime: Date): boolean {
    if (!timeSlots || timeSlots.length === 0) return true;

    const dayOfWeek = getDay(startTime);
    const timeString = format(startTime, 'HH:mm');

    return timeSlots.some(slot => {
      if (!slot.daysOfWeek.includes(dayOfWeek)) return false;
      
      const startSlotTime = parse(slot.startTime, 'HH:mm', new Date());
      const endSlotTime = parse(slot.endTime, 'HH:mm', new Date());
      const currentTime = parse(timeString, 'HH:mm', new Date());

      return isWithinInterval(currentTime, { start: startSlotTime, end: endSlotTime });
    });
  }

  // حساب التسعير بناءً على الفترات الزمنية
  private calculateTimeSlotPricing(
    baseRate: number, 
    startTime: Date, 
    durationMinutes: number, 
    timeSlots: TimeSlot[],
    breakdown: PriceBreakdown[]
  ): number {
    let totalPrice = 0;
    let currentTime = new Date(startTime);
    let remainingMinutes = durationMinutes;

    while (remainingMinutes > 0) {
      const applicableSlot = this.findTimeSlotForTime(timeSlots, currentTime);
      const multiplier = applicableSlot?.multiplier || 1;
      
      // حساب المدة في هذه الفترة
      let slotDuration = Math.min(remainingMinutes, 60); // حد أقصى ساعة واحدة لكل فترة
      
      const slotPrice = (baseRate * multiplier * slotDuration) / 60;
      totalPrice += slotPrice;

      breakdown.push({
        description: `${slotDuration} دقيقة × ${baseRate} × ${multiplier} (${format(currentTime, 'HH:mm')})`,
        amount: slotPrice,
        type: 'time_slot'
      });

      currentTime = new Date(currentTime.getTime() + slotDuration * 60000);
      remainingMinutes -= slotDuration;
    }

    return totalPrice;
  }

  // العثور على الفترة الزمنية للوقت المحدد
  private findTimeSlotForTime(timeSlots: TimeSlot[], time: Date): TimeSlot | undefined {
    const dayOfWeek = getDay(time);
    const timeString = format(time, 'HH:mm');

    return timeSlots.find(slot => {
      if (!slot.daysOfWeek.includes(dayOfWeek)) return false;
      
      const startSlotTime = parse(slot.startTime, 'HH:mm', new Date());
      const endSlotTime = parse(slot.endTime, 'HH:mm', new Date());
      const currentTime = parse(timeString, 'HH:mm', new Date());

      return isWithinInterval(currentTime, { start: startSlotTime, end: endSlotTime });
    });
  }

  // العثور على أفضل عرض متاح
  private findBestOffer(device: Device, gameType: 'single' | 'multiplayer', startTime: Date, durationMinutes: number): Offer | undefined {
    const now = new Date();
    
    const applicableOffers = this.offers.filter(offer => 
      offer.isActive &&
      isWithinInterval(startTime, { start: offer.validFrom, end: offer.validTo }) &&
      offer.applicableDevices.includes(device.type) &&
      (!offer.minimumDuration || durationMinutes >= offer.minimumDuration) &&
      (!offer.maximumDuration || durationMinutes <= offer.maximumDuration) &&
      (!offer.maxUsage || offer.usageCount < offer.maxUsage)
    );

    // ترتيب العروض حسب أفضل قيمة للعميل
    return applicableOffers.sort((a, b) => {
      const discountA = a.discountPercentage || 0;
      const discountB = b.discountPercentage || 0;
      return discountB - discountA;
    })[0];
  }

  // تطبيق العرض
  private applyOffer(basePrice: number, offer: Offer, durationMinutes: number, breakdown: PriceBreakdown[]): number {
    let finalPrice = basePrice;

    if (offer.type === 'discount' && offer.discountPercentage) {
      const discount = basePrice * (offer.discountPercentage / 100);
      finalPrice = basePrice - discount;
      
      breakdown.push({
        description: `خصم ${offer.discountPercentage}% - ${offer.name}`,
        amount: -discount,
        type: 'discount'
      });
    } else if (offer.type === 'package' && offer.fixedPrice) {
      finalPrice = offer.fixedPrice;
      
      breakdown.push({
        description: `باقة ${offer.name} - سعر ثابت`,
        amount: offer.fixedPrice - basePrice,
        type: 'package'
      });
    }

    // زيادة عداد الاستخدام
    offer.usageCount++;

    return finalPrice;
  }

  // ترتيب قواعد التسعير حسب الأولوية
  private sortPricingRulesByPriority(): void {
    this.pricingRules.sort((a, b) => b.priority - a.priority);
  }

  // تهيئة التسعير الافتراضي
  private initializeDefaultPricing(): void {
    // أسعار افتراضية للعب الفردي
    this.addPricingRule({
      name: 'سعر اللعب الفردي الافتراضي',
      gameType: 'single',
      deviceTypes: ['PS4', 'PS5', 'Xbox', 'PC', 'Nintendo'],
      pricePerHour: 15,
      isActive: true,
      priority: 1
    });

    // أسعار افتراضية للعب الجماعي
    this.addPricingRule({
      name: 'سعر اللعب الجماعي الافتراضي',
      gameType: 'multiplayer',
      deviceTypes: ['PS4', 'PS5', 'Xbox', 'PC', 'Nintendo'],
      pricePerHour: 20,
      isActive: true,
      priority: 1
    });

    // عرض الساعات السعيدة
    this.addOffer({
      name: 'الساعات السعيدة',
      description: 'خصم 25% من 2 ظهراً إلى 6 مساءً',
      type: 'happy_hour',
      discountPercentage: 25,
      applicableDevices: ['PS4', 'PS5', 'Xbox', 'PC', 'Nintendo'],
      validFrom: new Date(),
      validTo: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000), // سنة واحدة
      isActive: true
    });
  }

  // توليد معرف فريد
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  // الحصول على جميع قواعد التسعير
  getPricingRules(): PricingRule[] {
    return [...this.pricingRules];
  }

  // الحصول على جميع العروض
  getOffers(): Offer[] {
    return [...this.offers];
  }

  // الحصول على العروض النشطة
  getActiveOffers(): Offer[] {
    const now = new Date();
    return this.offers.filter(offer => 
      offer.isActive && 
      isWithinInterval(now, { start: offer.validFrom, end: offer.validTo })
    );
  }
}

// واجهة تفصيل السعر
interface PriceBreakdown {
  description: string;
  amount: number;
  type: 'base' | 'time_slot' | 'discount' | 'package';
}
