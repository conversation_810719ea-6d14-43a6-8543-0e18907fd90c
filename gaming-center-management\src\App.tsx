import React, { useState, useEffect } from 'react';
import { SystemManager } from './services/SystemManager';
import { SystemEvent } from './types';
import './App.css';

// إنشاء مثيل واحد من مدير النظام
const systemManager = new SystemManager();

function App() {
  const [activeTab, setActiveTab] = useState<'dashboard' | 'devices' | 'sessions' | 'pricing' | 'notifications'>('dashboard');
  const [dashboardStats, setDashboardStats] = useState(systemManager.getDashboardStats());

  useEffect(() => {
    // تحديث دوري للإحصائيات
    const interval = setInterval(() => {
      setDashboardStats(systemManager.getDashboardStats());
    }, 30000); // كل 30 ثانية

    return () => {
      clearInterval(interval);
    };
  }, []);

  return (
    <div className="app" dir="rtl">
      <header className="app-header">
        <h1>🎮 نظام إدارة مركز الألعاب</h1>
        <nav className="app-nav">
          <button
            className={`nav-button ${activeTab === 'dashboard' ? 'active' : ''}`}
            onClick={() => setActiveTab('dashboard')}
          >
            📊 لوحة التحكم
          </button>
          <button
            className={`nav-button ${activeTab === 'devices' ? 'active' : ''}`}
            onClick={() => setActiveTab('devices')}
          >
            🎮 الأجهزة
          </button>
          <button
            className={`nav-button ${activeTab === 'sessions' ? 'active' : ''}`}
            onClick={() => setActiveTab('sessions')}
          >
            ⏱️ الجلسات
          </button>
          <button
            className={`nav-button ${activeTab === 'pricing' ? 'active' : ''}`}
            onClick={() => setActiveTab('pricing')}
          >
            💰 الأسعار
          </button>
          <button
            className={`nav-button ${activeTab === 'notifications' ? 'active' : ''}`}
            onClick={() => setActiveTab('notifications')}
          >
            🔔 الإشعارات
          </button>
        </nav>
      </header>

      <main className="app-main">
        {activeTab === 'dashboard' && (
          <div className="dashboard">
            {/* إحصائيات سريعة */}
            <div className="stats-grid">
              <div className="stat-card">
                <div className="stat-icon">🎮</div>
                <div className="stat-content">
                  <h3>إجمالي الأجهزة</h3>
                  <p className="stat-number">{dashboardStats.totalDevices}</p>
                </div>
              </div>

              <div className="stat-card available">
                <div className="stat-icon">✅</div>
                <div className="stat-content">
                  <h3>الأجهزة المتاحة</h3>
                  <p className="stat-number">{dashboardStats.availableDevices}</p>
                </div>
              </div>

              <div className="stat-card occupied">
                <div className="stat-icon">🔴</div>
                <div className="stat-content">
                  <h3>الأجهزة المشغولة</h3>
                  <p className="stat-number">{dashboardStats.occupiedDevices}</p>
                </div>
              </div>

              <div className="stat-card revenue">
                <div className="stat-icon">💰</div>
                <div className="stat-content">
                  <h3>إيرادات اليوم</h3>
                  <p className="stat-number">{dashboardStats.todayRevenue.toFixed(2)} ريال</p>
                </div>
              </div>
            </div>

            <div className="card">
              <h2 className="card-title">🎮 مرحباً بك في نظام إدارة مركز الألعاب</h2>
              <p>النظام يعمل بنجاح! استخدم التبويبات أعلاه للتنقل بين الأقسام المختلفة.</p>

              <div style={{ marginTop: '2rem' }}>
                <h3>المميزات المتاحة:</h3>
                <ul style={{ textAlign: 'right', marginTop: '1rem' }}>
                  <li>📊 لوحة التحكم - عرض الإحصائيات العامة</li>
                  <li>🎮 إدارة الأجهزة - بدء وإنهاء الجلسات</li>
                  <li>⏱️ إدارة الجلسات - متابعة الجلسات النشطة</li>
                  <li>💰 إعدادات الأسعار - تخصيص الأسعار والعروض</li>
                  <li>🔔 الإشعارات - متابعة التنبيهات</li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'devices' && (
          <div className="device-grid-container">
            <div className="device-grid-header">
              <h2>🎮 إدارة الأجهزة</h2>
              <div className="device-stats">
                <span className="stat available">✅ متاح: {systemManager.devices.getAvailableDevices().length}</span>
                <span className="stat occupied">🔴 مشغول: {systemManager.devices.getOccupiedDevices().length}</span>
                <span className="stat paused">⏸️ متوقف: {systemManager.devices.getPausedDevices().length}</span>
                <span className="stat maintenance">🔧 صيانة: {systemManager.devices.getMaintenanceDevices().length}</span>
              </div>
            </div>

            <div className="devices-grid">
              {systemManager.devices.getAllDevices().map(device => (
                <div key={device.id} className={`device-card ${device.status}`}>
                  <div className="device-header">
                    <div className="device-info">
                      <span className="device-icon">
                        {device.type === 'PS5' ? '🎮' :
                         device.type === 'PS4' ? '🎮' :
                         device.type === 'Xbox' ? '🎯' :
                         device.type === 'PC' ? '💻' : '🕹️'}
                      </span>
                      <div>
                        <h3>{device.name}</h3>
                        <p className="device-type">{device.type}</p>
                      </div>
                    </div>
                    <div className="device-status" style={{
                      color: device.status === 'available' ? '#38a169' :
                             device.status === 'occupied' ? '#e53e3e' :
                             device.status === 'paused' ? '#ed8936' : '#718096'
                    }}>
                      {device.status === 'available' ? 'متاح' :
                       device.status === 'occupied' ? 'مشغول' :
                       device.status === 'paused' ? 'متوقف' : 'صيانة'}
                    </div>
                  </div>

                  {device.location && (
                    <div className="device-location">📍 {device.location}</div>
                  )}

                  <div className="device-price">💰 {device.pricePerHour} ريال/ساعة</div>

                  {device.currentSession && (
                    <div className="session-info">
                      <div className="session-customer">👤 {device.currentSession.customerName}</div>
                      <div className="session-details">
                        <span>🎮 {device.currentSession.gameType === 'single' ? 'فردي' : 'جماعي'}</span>
                        <span>⏰ {new Date(device.currentSession.startTime).toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' })}</span>
                      </div>
                    </div>
                  )}

                  <div className="device-actions">
                    {device.status === 'available' && (
                      <button
                        className="btn btn-success"
                        onClick={() => {
                          const customerName = prompt('اسم العميل:');
                          if (customerName) {
                            systemManager.devices.startSession(device.id, {
                              customerName,
                              gameType: 'single',
                              plannedDuration: 60
                            });
                            setDashboardStats(systemManager.getDashboardStats());
                          }
                        }}
                      >
                        ▶️ بدء جلسة
                      </button>
                    )}

                    {device.status === 'occupied' && (
                      <>
                        <button
                          className="btn btn-warning"
                          onClick={() => {
                            systemManager.devices.pauseSession(device.id);
                            setDashboardStats(systemManager.getDashboardStats());
                          }}
                        >
                          ⏸️ إيقاف
                        </button>
                        <button
                          className="btn btn-danger"
                          onClick={() => {
                            systemManager.devices.endSession(device.id);
                            setDashboardStats(systemManager.getDashboardStats());
                          }}
                        >
                          🛑 إنهاء
                        </button>
                      </>
                    )}

                    {device.status === 'paused' && (
                      <>
                        <button
                          className="btn btn-success"
                          onClick={() => {
                            systemManager.devices.resumeSession(device.id);
                            setDashboardStats(systemManager.getDashboardStats());
                          }}
                        >
                          ▶️ استئناف
                        </button>
                        <button
                          className="btn btn-danger"
                          onClick={() => {
                            systemManager.devices.endSession(device.id);
                            setDashboardStats(systemManager.getDashboardStats());
                          }}
                        >
                          🛑 إنهاء
                        </button>
                      </>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeTab === 'sessions' && (
          <div className="card">
            <h2>⏱️ إدارة الجلسات</h2>
            <p>قريباً سيتم إضافة واجهة إدارة الجلسات...</p>
          </div>
        )}

        {activeTab === 'pricing' && (
          <div className="card">
            <h2>💰 إعدادات الأسعار</h2>
            <p>قريباً سيتم إضافة واجهة إعدادات الأسعار...</p>
          </div>
        )}

        {activeTab === 'notifications' && (
          <div className="card">
            <h2>🔔 الإشعارات</h2>
            <p>قريباً سيتم إضافة واجهة الإشعارات...</p>
          </div>
        )}
      </main>
    </div>
  );
}

export default App;
