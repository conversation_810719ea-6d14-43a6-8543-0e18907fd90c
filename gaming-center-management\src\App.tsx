import React, { useState, useEffect } from 'react';
import './App.css';

function App() {
  const [activeTab, setActiveTab] = useState<'dashboard' | 'devices'>('dashboard');

  return (
    <div className="app" dir="rtl">
      <header className="app-header">
        <h1>🎮 نظام إدارة مركز الألعاب</h1>
        <nav className="app-nav">
          <button
            className={`nav-button ${activeTab === 'dashboard' ? 'active' : ''}`}
            onClick={() => setActiveTab('dashboard')}
          >
            📊 لوحة التحكم
          </button>
          <button
            className={`nav-button ${activeTab === 'devices' ? 'active' : ''}`}
            onClick={() => setActiveTab('devices')}
          >
            🎮 الأجهزة
          </button>
        </nav>
      </header>

      <main className="app-main">
        {activeTab === 'dashboard' ? (
          <div className="card">
            <h2>مرحباً بك في نظام إدارة مركز الألعاب</h2>
            <p>النظام يعمل بنجاح! 🎮</p>
          </div>
        ) : (
          <div className="card">
            <h2>إدارة الأجهزة</h2>
            <p>قريباً...</p>
          </div>
        )}
      </main>
    </div>
  );
}

export default App;
