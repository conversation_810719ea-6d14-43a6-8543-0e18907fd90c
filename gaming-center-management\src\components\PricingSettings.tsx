import React, { useState } from 'react';
import { PricingRule, Offer } from '../types';
import { SystemManager } from '../services/SystemManager';

interface PricingSettingsProps {
  systemManager: SystemManager;
}

export const PricingSettings: React.FC<PricingSettingsProps> = ({ systemManager }) => {
  const [activeTab, setActiveTab] = useState<'rules' | 'offers'>('rules');
  const [pricingRules, setPricingRules] = useState<PricingRule[]>(systemManager.pricing.getPricingRules());
  const [offers, setOffers] = useState<Offer[]>(systemManager.pricing.getOffers());
  const [showNewRuleModal, setShowNewRuleModal] = useState(false);
  const [showNewOfferModal, setShowNewOfferModal] = useState(false);

  const [newRule, setNewRule] = useState({
    name: '',
    gameType: 'both' as 'single' | 'multiplayer' | 'both',
    deviceTypes: [] as string[],
    pricePerHour: 15,
    isActive: true,
    priority: 1
  });

  const [newOffer, setNewOffer] = useState({
    name: '',
    description: '',
    type: 'discount' as 'discount' | 'package' | 'happy_hour',
    discountPercentage: 10,
    fixedPrice: 0,
    minimumDuration: 60,
    applicableDevices: [] as string[],
    validFrom: new Date().toISOString().slice(0, 16),
    validTo: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().slice(0, 16),
    isActive: true
  });

  const deviceTypes = ['PS4', 'PS5', 'Xbox', 'PC', 'Nintendo'];

  const handleCreateRule = () => {
    if (!newRule.name.trim() || newRule.deviceTypes.length === 0) return;

    const rule = systemManager.pricing.addPricingRule({
      name: newRule.name.trim(),
      gameType: newRule.gameType,
      deviceTypes: newRule.deviceTypes as any[],
      pricePerHour: newRule.pricePerHour,
      isActive: newRule.isActive,
      priority: newRule.priority
    });

    setPricingRules(systemManager.pricing.getPricingRules());
    setShowNewRuleModal(false);
    setNewRule({
      name: '',
      gameType: 'both',
      deviceTypes: [],
      pricePerHour: 15,
      isActive: true,
      priority: 1
    });
  };

  const handleCreateOffer = () => {
    if (!newOffer.name.trim() || newOffer.applicableDevices.length === 0) return;

    const offer = systemManager.pricing.addOffer({
      name: newOffer.name.trim(),
      description: newOffer.description.trim(),
      type: newOffer.type,
      discountPercentage: newOffer.type === 'discount' ? newOffer.discountPercentage : undefined,
      fixedPrice: newOffer.type === 'package' ? newOffer.fixedPrice : undefined,
      minimumDuration: newOffer.minimumDuration,
      applicableDevices: newOffer.applicableDevices as any[],
      validFrom: new Date(newOffer.validFrom),
      validTo: new Date(newOffer.validTo),
      isActive: newOffer.isActive
    });

    setOffers(systemManager.pricing.getOffers());
    setShowNewOfferModal(false);
    setNewOffer({
      name: '',
      description: '',
      type: 'discount',
      discountPercentage: 10,
      fixedPrice: 0,
      minimumDuration: 60,
      applicableDevices: [],
      validFrom: new Date().toISOString().slice(0, 16),
      validTo: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().slice(0, 16),
      isActive: true
    });
  };

  const toggleRuleStatus = (id: string) => {
    const rule = pricingRules.find(r => r.id === id);
    if (rule) {
      systemManager.pricing.updatePricingRule(id, { isActive: !rule.isActive });
      setPricingRules(systemManager.pricing.getPricingRules());
    }
  };

  const toggleOfferStatus = (id: string) => {
    const offer = offers.find(o => o.id === id);
    if (offer) {
      systemManager.pricing.updateOffer(id, { isActive: !offer.isActive });
      setOffers(systemManager.pricing.getOffers());
    }
  };

  const deleteRule = (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذه القاعدة؟')) {
      systemManager.pricing.deletePricingRule(id);
      setPricingRules(systemManager.pricing.getPricingRules());
    }
  };

  const deleteOffer = (id: string) => {
    if (confirm('هل أنت متأكد من حذف هذا العرض؟')) {
      systemManager.pricing.deleteOffer(id);
      setOffers(systemManager.pricing.getOffers());
    }
  };

  const handleDeviceTypeChange = (deviceType: string, checked: boolean, target: 'rule' | 'offer') => {
    if (target === 'rule') {
      setNewRule(prev => ({
        ...prev,
        deviceTypes: checked 
          ? [...prev.deviceTypes, deviceType]
          : prev.deviceTypes.filter(t => t !== deviceType)
      }));
    } else {
      setNewOffer(prev => ({
        ...prev,
        applicableDevices: checked 
          ? [...prev.applicableDevices, deviceType]
          : prev.applicableDevices.filter(t => t !== deviceType)
      }));
    }
  };

  return (
    <div className="pricing-settings">
      <div className="pricing-header">
        <h2>💰 إعدادات الأسعار</h2>
        
        <div className="pricing-tabs">
          <button
            className={`tab-btn ${activeTab === 'rules' ? 'active' : ''}`}
            onClick={() => setActiveTab('rules')}
          >
            قواعد التسعير
          </button>
          <button
            className={`tab-btn ${activeTab === 'offers' ? 'active' : ''}`}
            onClick={() => setActiveTab('offers')}
          >
            العروض والخصومات
          </button>
        </div>
      </div>

      {activeTab === 'rules' && (
        <div className="pricing-rules">
          <div className="section-header">
            <h3>قواعد التسعير</h3>
            <button 
              className="btn btn-primary"
              onClick={() => setShowNewRuleModal(true)}
            >
              ➕ إضافة قاعدة جديدة
            </button>
          </div>

          <div className="rules-list">
            {pricingRules.map(rule => (
              <div key={rule.id} className={`rule-card ${!rule.isActive ? 'inactive' : ''}`}>
                <div className="rule-header">
                  <h4>{rule.name}</h4>
                  <div className="rule-actions">
                    <button
                      className={`btn btn-sm ${rule.isActive ? 'btn-warning' : 'btn-success'}`}
                      onClick={() => toggleRuleStatus(rule.id)}
                    >
                      {rule.isActive ? '⏸️ إيقاف' : '▶️ تفعيل'}
                    </button>
                    <button
                      className="btn btn-danger btn-sm"
                      onClick={() => deleteRule(rule.id)}
                    >
                      🗑️ حذف
                    </button>
                  </div>
                </div>
                
                <div className="rule-details">
                  <div className="rule-info">
                    <span><strong>نوع اللعب:</strong> {rule.gameType === 'both' ? 'الكل' : rule.gameType === 'single' ? 'فردي' : 'جماعي'}</span>
                    <span><strong>الأجهزة:</strong> {rule.deviceTypes.join(', ')}</span>
                    <span><strong>السعر:</strong> {rule.pricePerHour} ريال/ساعة</span>
                    <span><strong>الأولوية:</strong> {rule.priority}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {activeTab === 'offers' && (
        <div className="pricing-offers">
          <div className="section-header">
            <h3>العروض والخصومات</h3>
            <button 
              className="btn btn-primary"
              onClick={() => setShowNewOfferModal(true)}
            >
              ➕ إضافة عرض جديد
            </button>
          </div>

          <div className="offers-list">
            {offers.map(offer => (
              <div key={offer.id} className={`offer-card ${!offer.isActive ? 'inactive' : ''}`}>
                <div className="offer-header">
                  <h4>{offer.name}</h4>
                  <div className="offer-actions">
                    <button
                      className={`btn btn-sm ${offer.isActive ? 'btn-warning' : 'btn-success'}`}
                      onClick={() => toggleOfferStatus(offer.id)}
                    >
                      {offer.isActive ? '⏸️ إيقاف' : '▶️ تفعيل'}
                    </button>
                    <button
                      className="btn btn-danger btn-sm"
                      onClick={() => deleteOffer(offer.id)}
                    >
                      🗑️ حذف
                    </button>
                  </div>
                </div>
                
                <div className="offer-details">
                  <p>{offer.description}</p>
                  <div className="offer-info">
                    <span><strong>النوع:</strong> {offer.type === 'discount' ? 'خصم' : offer.type === 'package' ? 'باقة' : 'ساعات سعيدة'}</span>
                    {offer.discountPercentage && <span><strong>الخصم:</strong> {offer.discountPercentage}%</span>}
                    {offer.fixedPrice && <span><strong>السعر الثابت:</strong> {offer.fixedPrice} ريال</span>}
                    <span><strong>الحد الأدنى:</strong> {offer.minimumDuration} دقيقة</span>
                    <span><strong>الأجهزة:</strong> {offer.applicableDevices.join(', ')}</span>
                    <span><strong>مرات الاستخدام:</strong> {offer.usageCount}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* نافذة إضافة قاعدة جديدة */}
      {showNewRuleModal && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h3>إضافة قاعدة تسعير جديدة</h3>
              <button className="modal-close" onClick={() => setShowNewRuleModal(false)}>✕</button>
            </div>
            
            <div className="modal-body">
              <div className="form-group">
                <label>اسم القاعدة *</label>
                <input
                  type="text"
                  value={newRule.name}
                  onChange={(e) => setNewRule({...newRule, name: e.target.value})}
                  placeholder="مثال: أسعار نهاية الأسبوع"
                />
              </div>

              <div className="form-group">
                <label>نوع اللعب</label>
                <select
                  value={newRule.gameType}
                  onChange={(e) => setNewRule({...newRule, gameType: e.target.value as any})}
                >
                  <option value="both">الكل</option>
                  <option value="single">فردي</option>
                  <option value="multiplayer">جماعي</option>
                </select>
              </div>

              <div className="form-group">
                <label>أنواع الأجهزة *</label>
                <div className="checkbox-group">
                  {deviceTypes.map(type => (
                    <label key={type} className="checkbox-label">
                      <input
                        type="checkbox"
                        checked={newRule.deviceTypes.includes(type)}
                        onChange={(e) => handleDeviceTypeChange(type, e.target.checked, 'rule')}
                      />
                      {type}
                    </label>
                  ))}
                </div>
              </div>

              <div className="form-group">
                <label>السعر بالساعة (ريال)</label>
                <input
                  type="number"
                  value={newRule.pricePerHour}
                  onChange={(e) => setNewRule({...newRule, pricePerHour: parseFloat(e.target.value) || 0})}
                  min="0"
                  step="0.5"
                />
              </div>

              <div className="form-group">
                <label>الأولوية (رقم أعلى = أولوية أكبر)</label>
                <input
                  type="number"
                  value={newRule.priority}
                  onChange={(e) => setNewRule({...newRule, priority: parseInt(e.target.value) || 1})}
                  min="1"
                />
              </div>
            </div>

            <div className="modal-footer">
              <button className="btn btn-secondary" onClick={() => setShowNewRuleModal(false)}>
                إلغاء
              </button>
              <button 
                className="btn btn-primary" 
                onClick={handleCreateRule}
                disabled={!newRule.name.trim() || newRule.deviceTypes.length === 0}
              >
                إضافة القاعدة
              </button>
            </div>
          </div>
        </div>
      )}

      {/* نافذة إضافة عرض جديد */}
      {showNewOfferModal && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h3>إضافة عرض جديد</h3>
              <button className="modal-close" onClick={() => setShowNewOfferModal(false)}>✕</button>
            </div>
            
            <div className="modal-body">
              <div className="form-group">
                <label>اسم العرض *</label>
                <input
                  type="text"
                  value={newOffer.name}
                  onChange={(e) => setNewOffer({...newOffer, name: e.target.value})}
                  placeholder="مثال: خصم الطلاب"
                />
              </div>

              <div className="form-group">
                <label>الوصف</label>
                <textarea
                  value={newOffer.description}
                  onChange={(e) => setNewOffer({...newOffer, description: e.target.value})}
                  placeholder="وصف العرض..."
                  rows={3}
                />
              </div>

              <div className="form-group">
                <label>نوع العرض</label>
                <select
                  value={newOffer.type}
                  onChange={(e) => setNewOffer({...newOffer, type: e.target.value as any})}
                >
                  <option value="discount">خصم بالنسبة المئوية</option>
                  <option value="package">باقة بسعر ثابت</option>
                  <option value="happy_hour">ساعات سعيدة</option>
                </select>
              </div>

              {newOffer.type === 'discount' && (
                <div className="form-group">
                  <label>نسبة الخصم (%)</label>
                  <input
                    type="number"
                    value={newOffer.discountPercentage}
                    onChange={(e) => setNewOffer({...newOffer, discountPercentage: parseFloat(e.target.value) || 0})}
                    min="0"
                    max="100"
                    step="1"
                  />
                </div>
              )}

              {newOffer.type === 'package' && (
                <div className="form-group">
                  <label>السعر الثابت (ريال)</label>
                  <input
                    type="number"
                    value={newOffer.fixedPrice}
                    onChange={(e) => setNewOffer({...newOffer, fixedPrice: parseFloat(e.target.value) || 0})}
                    min="0"
                    step="0.5"
                  />
                </div>
              )}

              <div className="form-group">
                <label>الحد الأدنى للمدة (دقيقة)</label>
                <input
                  type="number"
                  value={newOffer.minimumDuration}
                  onChange={(e) => setNewOffer({...newOffer, minimumDuration: parseInt(e.target.value) || 0})}
                  min="0"
                  step="15"
                />
              </div>

              <div className="form-group">
                <label>أنواع الأجهزة المطبقة *</label>
                <div className="checkbox-group">
                  {deviceTypes.map(type => (
                    <label key={type} className="checkbox-label">
                      <input
                        type="checkbox"
                        checked={newOffer.applicableDevices.includes(type)}
                        onChange={(e) => handleDeviceTypeChange(type, e.target.checked, 'offer')}
                      />
                      {type}
                    </label>
                  ))}
                </div>
              </div>

              <div className="form-group">
                <label>تاريخ البداية</label>
                <input
                  type="datetime-local"
                  value={newOffer.validFrom}
                  onChange={(e) => setNewOffer({...newOffer, validFrom: e.target.value})}
                />
              </div>

              <div className="form-group">
                <label>تاريخ النهاية</label>
                <input
                  type="datetime-local"
                  value={newOffer.validTo}
                  onChange={(e) => setNewOffer({...newOffer, validTo: e.target.value})}
                />
              </div>
            </div>

            <div className="modal-footer">
              <button className="btn btn-secondary" onClick={() => setShowNewOfferModal(false)}>
                إلغاء
              </button>
              <button 
                className="btn btn-primary" 
                onClick={handleCreateOffer}
                disabled={!newOffer.name.trim() || newOffer.applicableDevices.length === 0}
              >
                إضافة العرض
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
