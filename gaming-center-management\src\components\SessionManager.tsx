import React, { useState, useEffect } from 'react';
import { Session } from '../types';
import { SystemManager } from '../services/SystemManager';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';

interface SessionManagerProps {
  systemManager: SystemManager;
}

export const SessionManager: React.FC<SessionManagerProps> = ({ systemManager }) => {
  const [sessions, setSessions] = useState<Session[]>([]);
  const [filter, setFilter] = useState<'all' | 'active' | 'completed' | 'paused'>('all');

  useEffect(() => {
    const updateSessions = () => {
      const activeSessions = systemManager.time.getActiveSessions();
      // في تطبيق حقيقي، ستحتاج لتخزين الجلسات المكتملة في قاعدة بيانات
      setSessions(activeSessions);
    };

    updateSessions();
    systemManager.addEventListener(updateSessions);
    const interval = setInterval(updateSessions, 5000);

    return () => {
      systemManager.removeEventListener(updateSessions);
      clearInterval(interval);
    };
  }, [systemManager]);

  const filteredSessions = sessions.filter(session => {
    if (filter === 'all') return true;
    return session.status === filter;
  });

  const getStatusColor = (status: Session['status']) => {
    switch (status) {
      case 'active': return '#38a169';
      case 'paused': return '#ed8936';
      case 'completed': return '#718096';
      case 'cancelled': return '#e53e3e';
      default: return '#4299e1';
    }
  };

  const getStatusText = (status: Session['status']) => {
    switch (status) {
      case 'active': return 'نشط';
      case 'paused': return 'متوقف';
      case 'completed': return 'مكتمل';
      case 'cancelled': return 'ملغي';
      default: return 'غير معروف';
    }
  };

  const formatDuration = (minutes?: number) => {
    if (!minutes) return '--';
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}س ${mins}د` : `${mins}د`;
  };

  const formatCurrency = (amount: number) => {
    return `${amount.toFixed(2)} ريال`;
  };

  const getRemainingTime = (session: Session) => {
    if (session.status !== 'active') return null;
    return systemManager.time.getRemainingTime(session.id);
  };

  const handleSessionAction = (session: Session, action: string) => {
    const device = systemManager.devices.getDevice(session.deviceId);
    if (!device) return;

    switch (action) {
      case 'pause':
        systemManager.devices.pauseSession(device.id);
        break;
      case 'resume':
        systemManager.devices.resumeSession(device.id);
        break;
      case 'end':
        systemManager.devices.endSession(device.id);
        break;
    }
  };

  const getDeviceIcon = (deviceId: string) => {
    const device = systemManager.devices.getDevice(deviceId);
    if (!device) return '🎮';
    
    switch (device.type) {
      case 'PS5': return '🎮';
      case 'PS4': return '🎮';
      case 'Xbox': return '🎯';
      case 'PC': return '💻';
      case 'Nintendo': return '🕹️';
      default: return '🎮';
    }
  };

  const getDeviceName = (deviceId: string) => {
    const device = systemManager.devices.getDevice(deviceId);
    return device?.name || 'جهاز غير معروف';
  };

  return (
    <div className="session-manager">
      <div className="session-manager-header">
        <h2>⏱️ إدارة الجلسات</h2>
        
        <div className="session-filters">
          <button
            className={`filter-btn ${filter === 'all' ? 'active' : ''}`}
            onClick={() => setFilter('all')}
          >
            الكل ({sessions.length})
          </button>
          <button
            className={`filter-btn ${filter === 'active' ? 'active' : ''}`}
            onClick={() => setFilter('active')}
          >
            نشط ({sessions.filter(s => s.status === 'active').length})
          </button>
          <button
            className={`filter-btn ${filter === 'paused' ? 'active' : ''}`}
            onClick={() => setFilter('paused')}
          >
            متوقف ({sessions.filter(s => s.status === 'paused').length})
          </button>
          <button
            className={`filter-btn ${filter === 'completed' ? 'active' : ''}`}
            onClick={() => setFilter('completed')}
          >
            مكتمل ({sessions.filter(s => s.status === 'completed').length})
          </button>
        </div>
      </div>

      {filteredSessions.length === 0 ? (
        <div className="empty-state">
          لا توجد جلسات {filter === 'all' ? '' : getStatusText(filter as Session['status'])}
        </div>
      ) : (
        <div className="sessions-table">
          <div className="table-header">
            <div className="table-cell">الجهاز</div>
            <div className="table-cell">العميل</div>
            <div className="table-cell">النوع</div>
            <div className="table-cell">وقت البداية</div>
            <div className="table-cell">المدة المخططة</div>
            <div className="table-cell">الوقت المتبقي</div>
            <div className="table-cell">الحالة</div>
            <div className="table-cell">التكلفة</div>
            <div className="table-cell">الإجراءات</div>
          </div>

          {filteredSessions.map(session => {
            const remainingTime = getRemainingTime(session);
            
            return (
              <div key={session.id} className="table-row">
                <div className="table-cell">
                  <div className="device-info">
                    <span className="device-icon">{getDeviceIcon(session.deviceId)}</span>
                    <span>{getDeviceName(session.deviceId)}</span>
                  </div>
                </div>
                
                <div className="table-cell">
                  <div className="customer-info">
                    <span className="customer-name">{session.customerName}</span>
                  </div>
                </div>
                
                <div className="table-cell">
                  <span className={`game-type ${session.gameType}`}>
                    {session.gameType === 'single' ? '👤 فردي' : '👥 جماعي'}
                  </span>
                </div>
                
                <div className="table-cell">
                  {format(session.startTime, 'HH:mm - dd/MM', { locale: ar })}
                </div>
                
                <div className="table-cell">
                  {session.plannedDuration ? formatDuration(session.plannedDuration) : 'مفتوح'}
                </div>
                
                <div className="table-cell">
                  {remainingTime !== null ? (
                    <span className={remainingTime <= 10 ? 'time-warning' : ''}>
                      {formatDuration(remainingTime)}
                    </span>
                  ) : '--'}
                </div>
                
                <div className="table-cell">
                  <span 
                    className="status-badge"
                    style={{ color: getStatusColor(session.status) }}
                  >
                    {getStatusText(session.status)}
                  </span>
                </div>
                
                <div className="table-cell">
                  {formatCurrency(session.totalCost)}
                </div>
                
                <div className="table-cell">
                  <div className="session-actions">
                    {session.status === 'active' && (
                      <button
                        className="btn btn-warning btn-sm"
                        onClick={() => handleSessionAction(session, 'pause')}
                      >
                        ⏸️
                      </button>
                    )}
                    
                    {session.status === 'paused' && (
                      <button
                        className="btn btn-success btn-sm"
                        onClick={() => handleSessionAction(session, 'resume')}
                      >
                        ▶️
                      </button>
                    )}
                    
                    {(session.status === 'active' || session.status === 'paused') && (
                      <button
                        className="btn btn-danger btn-sm"
                        onClick={() => handleSessionAction(session, 'end')}
                      >
                        🛑
                      </button>
                    )}
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
};
