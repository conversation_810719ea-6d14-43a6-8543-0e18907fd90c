import React, { useState, useEffect } from 'react';
import { Device } from '../types';
import { SystemManager } from '../services/SystemManager';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';

interface DeviceGridProps {
  systemManager: SystemManager;
}

export const DeviceGrid: React.FC<DeviceGridProps> = ({ systemManager }) => {
  const [devices, setDevices] = useState<Device[]>(systemManager.devices.getAllDevices());
  const [selectedDevice, setSelectedDevice] = useState<Device | null>(null);
  const [showNewSessionModal, setShowNewSessionModal] = useState(false);
  const [newSessionData, setNewSessionData] = useState({
    customerName: '',
    gameType: 'single' as 'single' | 'multiplayer',
    plannedDuration: 60,
    notes: ''
  });

  useEffect(() => {
    const updateDevices = () => {
      setDevices(systemManager.devices.getAllDevices());
    };

    systemManager.addEventListener(updateDevices);
    const interval = setInterval(updateDevices, 5000);

    return () => {
      systemManager.removeEventListener(updateDevices);
      clearInterval(interval);
    };
  }, [systemManager]);

  const getDeviceIcon = (type: Device['type']) => {
    switch (type) {
      case 'PS5': return '🎮';
      case 'PS4': return '🎮';
      case 'Xbox': return '🎯';
      case 'PC': return '💻';
      case 'Nintendo': return '🕹️';
      default: return '🎮';
    }
  };

  const getStatusColor = (status: Device['status']) => {
    switch (status) {
      case 'available': return '#38a169';
      case 'occupied': return '#e53e3e';
      case 'paused': return '#ed8936';
      case 'maintenance': return '#718096';
      default: return '#4299e1';
    }
  };

  const getStatusText = (status: Device['status']) => {
    switch (status) {
      case 'available': return 'متاح';
      case 'occupied': return 'مشغول';
      case 'paused': return 'متوقف';
      case 'maintenance': return 'صيانة';
      default: return 'غير معروف';
    }
  };

  const handleStartSession = (device: Device) => {
    setSelectedDevice(device);
    setShowNewSessionModal(true);
  };

  const handleCreateSession = () => {
    if (!selectedDevice || !newSessionData.customerName.trim()) return;

    const session = systemManager.devices.startSession(selectedDevice.id, {
      customerName: newSessionData.customerName.trim(),
      gameType: newSessionData.gameType,
      plannedDuration: newSessionData.plannedDuration > 0 ? newSessionData.plannedDuration : undefined,
      notes: newSessionData.notes.trim() || undefined
    });

    if (session) {
      setShowNewSessionModal(false);
      setSelectedDevice(null);
      setNewSessionData({
        customerName: '',
        gameType: 'single',
        plannedDuration: 60,
        notes: ''
      });
      setDevices(systemManager.devices.getAllDevices());
    }
  };

  const handleDeviceAction = (device: Device, action: string) => {
    switch (action) {
      case 'pause':
        systemManager.devices.pauseSession(device.id);
        break;
      case 'resume':
        systemManager.devices.resumeSession(device.id);
        break;
      case 'end':
        systemManager.devices.endSession(device.id);
        break;
      case 'maintenance':
        systemManager.devices.setDeviceForMaintenance(device.id);
        break;
      case 'available':
        systemManager.devices.setDeviceAvailable(device.id);
        break;
    }
    setDevices(systemManager.devices.getAllDevices());
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}س ${mins}د` : `${mins}د`;
  };

  const getRemainingTime = (device: Device) => {
    if (!device.currentSession) return null;
    return systemManager.time.getRemainingTime(device.currentSession.id);
  };

  return (
    <div className="device-grid-container">
      <div className="device-grid-header">
        <h2>🎮 إدارة الأجهزة</h2>
        <div className="device-stats">
          <span className="stat available">✅ متاح: {devices.filter(d => d.status === 'available').length}</span>
          <span className="stat occupied">🔴 مشغول: {devices.filter(d => d.status === 'occupied').length}</span>
          <span className="stat paused">⏸️ متوقف: {devices.filter(d => d.status === 'paused').length}</span>
          <span className="stat maintenance">🔧 صيانة: {devices.filter(d => d.status === 'maintenance').length}</span>
        </div>
      </div>

      <div className="devices-grid">
        {devices.map(device => {
          const remainingTime = getRemainingTime(device);
          
          return (
            <div 
              key={device.id} 
              className={`device-card ${device.status}`}
              style={{ borderColor: getStatusColor(device.status) }}
            >
              <div className="device-header">
                <div className="device-info">
                  <span className="device-icon">{getDeviceIcon(device.type)}</span>
                  <div>
                    <h3>{device.name}</h3>
                    <p className="device-type">{device.type}</p>
                  </div>
                </div>
                <div 
                  className="device-status"
                  style={{ color: getStatusColor(device.status) }}
                >
                  {getStatusText(device.status)}
                </div>
              </div>

              {device.location && (
                <div className="device-location">
                  📍 {device.location}
                </div>
              )}

              <div className="device-price">
                💰 {device.pricePerHour} ريال/ساعة
              </div>

              {device.currentSession && (
                <div className="session-info">
                  <div className="session-customer">
                    👤 {device.currentSession.customerName}
                  </div>
                  <div className="session-details">
                    <span>🎮 {device.currentSession.gameType === 'single' ? 'فردي' : 'جماعي'}</span>
                    <span>⏰ {format(device.currentSession.startTime, 'HH:mm', { locale: ar })}</span>
                  </div>
                  {remainingTime !== null && (
                    <div className={`remaining-time ${remainingTime <= 10 ? 'warning' : ''}`}>
                      ⏳ باقي {formatDuration(remainingTime)}
                    </div>
                  )}
                </div>
              )}

              <div className="device-actions">
                {device.status === 'available' && (
                  <>
                    <button 
                      className="btn btn-success"
                      onClick={() => handleStartSession(device)}
                    >
                      ▶️ بدء جلسة
                    </button>
                    <button 
                      className="btn btn-secondary"
                      onClick={() => handleDeviceAction(device, 'maintenance')}
                    >
                      🔧 صيانة
                    </button>
                  </>
                )}

                {device.status === 'occupied' && (
                  <>
                    <button 
                      className="btn btn-warning"
                      onClick={() => handleDeviceAction(device, 'pause')}
                    >
                      ⏸️ إيقاف
                    </button>
                    <button 
                      className="btn btn-danger"
                      onClick={() => handleDeviceAction(device, 'end')}
                    >
                      🛑 إنهاء
                    </button>
                  </>
                )}

                {device.status === 'paused' && (
                  <>
                    <button 
                      className="btn btn-success"
                      onClick={() => handleDeviceAction(device, 'resume')}
                    >
                      ▶️ استئناف
                    </button>
                    <button 
                      className="btn btn-danger"
                      onClick={() => handleDeviceAction(device, 'end')}
                    >
                      🛑 إنهاء
                    </button>
                  </>
                )}

                {device.status === 'maintenance' && (
                  <button 
                    className="btn btn-primary"
                    onClick={() => handleDeviceAction(device, 'available')}
                  >
                    ✅ جاهز
                  </button>
                )}
              </div>
            </div>
          );
        })}
      </div>

      {/* نافذة إنشاء جلسة جديدة */}
      {showNewSessionModal && selectedDevice && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h3>بدء جلسة جديدة - {selectedDevice.name}</h3>
              <button 
                className="modal-close"
                onClick={() => setShowNewSessionModal(false)}
              >
                ✕
              </button>
            </div>

            <div className="modal-body">
              <div className="form-group">
                <label>اسم العميل *</label>
                <input
                  type="text"
                  value={newSessionData.customerName}
                  onChange={(e) => setNewSessionData({...newSessionData, customerName: e.target.value})}
                  placeholder="أدخل اسم العميل"
                  required
                />
              </div>

              <div className="form-group">
                <label>نوع اللعب</label>
                <select
                  value={newSessionData.gameType}
                  onChange={(e) => setNewSessionData({...newSessionData, gameType: e.target.value as 'single' | 'multiplayer'})}
                >
                  <option value="single">فردي</option>
                  <option value="multiplayer">جماعي</option>
                </select>
              </div>

              <div className="form-group">
                <label>المدة المخططة (دقيقة) - اتركها 0 للوقت المفتوح</label>
                <input
                  type="number"
                  value={newSessionData.plannedDuration}
                  onChange={(e) => setNewSessionData({...newSessionData, plannedDuration: parseInt(e.target.value) || 0})}
                  min="0"
                  step="15"
                />
              </div>

              <div className="form-group">
                <label>ملاحظات</label>
                <textarea
                  value={newSessionData.notes}
                  onChange={(e) => setNewSessionData({...newSessionData, notes: e.target.value})}
                  placeholder="ملاحظات إضافية (اختياري)"
                  rows={3}
                />
              </div>
            </div>

            <div className="modal-footer">
              <button 
                className="btn btn-secondary"
                onClick={() => setShowNewSessionModal(false)}
              >
                إلغاء
              </button>
              <button 
                className="btn btn-primary"
                onClick={handleCreateSession}
                disabled={!newSessionData.customerName.trim()}
              >
                بدء الجلسة
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
