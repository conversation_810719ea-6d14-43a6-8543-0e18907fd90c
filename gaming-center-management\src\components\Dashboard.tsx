import React from 'react';
import { DashboardStats } from '../types';
import { SystemManager } from '../services/SystemManager';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';

interface DashboardProps {
  stats: DashboardStats;
  systemManager: SystemManager;
}

export const Dashboard: React.FC<DashboardProps> = ({ stats, systemManager }) => {
  const activeSessions = systemManager.time.getActiveSessions();
  const recentNotifications = systemManager.getNotifications(5);

  const getDeviceTypeIcon = (type: string) => {
    switch (type) {
      case 'PS5': return '🎮';
      case 'PS4': return '🎮';
      case 'Xbox': return '🎯';
      case 'PC': return '💻';
      case 'Nintendo': return '🕹️';
      default: return '🎮';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return '#38a169';
      case 'paused': return '#ed8936';
      case 'completed': return '#718096';
      default: return '#4299e1';
    }
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return hours > 0 ? `${hours}س ${mins}د` : `${mins}د`;
  };

  const formatCurrency = (amount: number) => {
    return `${amount.toFixed(2)} ريال`;
  };

  return (
    <div className="dashboard">
      {/* إحصائيات سريعة */}
      <div className="stats-grid">
        <div className="stat-card">
          <div className="stat-icon">🎮</div>
          <div className="stat-content">
            <h3>إجمالي الأجهزة</h3>
            <p className="stat-number">{stats.totalDevices}</p>
          </div>
        </div>

        <div className="stat-card available">
          <div className="stat-icon">✅</div>
          <div className="stat-content">
            <h3>الأجهزة المتاحة</h3>
            <p className="stat-number">{stats.availableDevices}</p>
          </div>
        </div>

        <div className="stat-card occupied">
          <div className="stat-icon">🔴</div>
          <div className="stat-content">
            <h3>الأجهزة المشغولة</h3>
            <p className="stat-number">{stats.occupiedDevices}</p>
          </div>
        </div>

        <div className="stat-card paused">
          <div className="stat-icon">⏸️</div>
          <div className="stat-content">
            <h3>الأجهزة المتوقفة</h3>
            <p className="stat-number">{stats.pausedDevices}</p>
          </div>
        </div>

        <div className="stat-card maintenance">
          <div className="stat-icon">🔧</div>
          <div className="stat-content">
            <h3>في الصيانة</h3>
            <p className="stat-number">{stats.maintenanceDevices}</p>
          </div>
        </div>

        <div className="stat-card revenue">
          <div className="stat-icon">💰</div>
          <div className="stat-content">
            <h3>إيرادات اليوم</h3>
            <p className="stat-number">{formatCurrency(stats.todayRevenue)}</p>
          </div>
        </div>

        <div className="stat-card sessions">
          <div className="stat-icon">📊</div>
          <div className="stat-content">
            <h3>جلسات اليوم</h3>
            <p className="stat-number">{stats.todaySessions}</p>
          </div>
        </div>

        <div className="stat-card duration">
          <div className="stat-icon">⏱️</div>
          <div className="stat-content">
            <h3>متوسط مدة الجلسة</h3>
            <p className="stat-number">{formatDuration(stats.averageSessionDuration)}</p>
          </div>
        </div>
      </div>

      <div className="dashboard-content">
        {/* الجلسات النشطة */}
        <div className="card">
          <h2 className="card-title">
            ⚡ الجلسات النشطة ({activeSessions.length})
          </h2>
          {activeSessions.length === 0 ? (
            <p className="empty-state">لا توجد جلسات نشطة حالياً</p>
          ) : (
            <div className="sessions-list">
              {activeSessions.map(session => {
                const device = systemManager.devices.getDevice(session.deviceId);
                const remainingTime = systemManager.time.getRemainingTime(session.id);
                
                return (
                  <div key={session.id} className="session-item">
                    <div className="session-info">
                      <div className="session-header">
                        <span className="device-name">
                          {getDeviceTypeIcon(device?.type || 'PS4')} {device?.name}
                        </span>
                        <span 
                          className="session-status"
                          style={{ color: getStatusColor(session.status) }}
                        >
                          {session.status === 'active' ? '🟢 نشط' : '⏸️ متوقف'}
                        </span>
                      </div>
                      <div className="session-details">
                        <span>👤 {session.customerName}</span>
                        <span>🎮 {session.gameType === 'single' ? 'فردي' : 'جماعي'}</span>
                        <span>⏰ {format(session.startTime, 'HH:mm', { locale: ar })}</span>
                        {remainingTime !== null && (
                          <span className={remainingTime <= 10 ? 'time-warning' : ''}>
                            ⏳ باقي {formatDuration(remainingTime)}
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="session-actions">
                      {session.status === 'active' ? (
                        <button 
                          className="btn btn-warning btn-sm"
                          onClick={() => systemManager.devices.pauseSession(session.deviceId)}
                        >
                          ⏸️ إيقاف
                        </button>
                      ) : (
                        <button 
                          className="btn btn-success btn-sm"
                          onClick={() => systemManager.devices.resumeSession(session.deviceId)}
                        >
                          ▶️ استئناف
                        </button>
                      )}
                      <button 
                        className="btn btn-danger btn-sm"
                        onClick={() => systemManager.devices.endSession(session.deviceId)}
                      >
                        🛑 إنهاء
                      </button>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>

        {/* الإشعارات الأخيرة */}
        <div className="card">
          <h2 className="card-title">
            🔔 الإشعارات الأخيرة
          </h2>
          {recentNotifications.length === 0 ? (
            <p className="empty-state">لا توجد إشعارات</p>
          ) : (
            <div className="notifications-list">
              {recentNotifications.map(notification => (
                <div 
                  key={notification.id} 
                  className={`notification-item ${!notification.isRead ? 'unread' : ''}`}
                >
                  <div className="notification-content">
                    <h4>{notification.title}</h4>
                    <p>{notification.message}</p>
                    <small>{format(notification.createdAt, 'HH:mm - dd/MM', { locale: ar })}</small>
                  </div>
                  {!notification.isRead && (
                    <button 
                      className="btn btn-secondary btn-sm"
                      onClick={() => systemManager.markNotificationAsRead(notification.id)}
                    >
                      ✓
                    </button>
                  )}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};
