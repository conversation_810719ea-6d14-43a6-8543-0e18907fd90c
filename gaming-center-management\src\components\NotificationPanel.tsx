import React, { useState, useEffect } from 'react';
import { Notification } from '../types';
import { SystemManager } from '../services/SystemManager';
import { format } from 'date-fns';
import { ar } from 'date-fns/locale';

interface NotificationPanelProps {
  systemManager: SystemManager;
}

export const NotificationPanel: React.FC<NotificationPanelProps> = ({ systemManager }) => {
  const [notifications, setNotifications] = useState<Notification[]>(systemManager.getNotifications());
  const [filter, setFilter] = useState<'all' | 'unread' | 'time_warning' | 'time_up' | 'payment_due'>('all');

  useEffect(() => {
    const updateNotifications = () => {
      setNotifications(systemManager.getNotifications());
    };

    systemManager.addEventListener(updateNotifications);
    const interval = setInterval(updateNotifications, 5000);

    return () => {
      systemManager.removeEventListener(updateNotifications);
      clearInterval(interval);
    };
  }, [systemManager]);

  const filteredNotifications = notifications.filter(notification => {
    if (filter === 'all') return true;
    if (filter === 'unread') return !notification.isRead;
    return notification.type === filter;
  });

  const getNotificationIcon = (type: Notification['type']) => {
    switch (type) {
      case 'time_warning': return '⚠️';
      case 'time_up': return '⏰';
      case 'payment_due': return '💰';
      case 'device_available': return '✅';
      default: return '🔔';
    }
  };

  const getNotificationColor = (type: Notification['type']) => {
    switch (type) {
      case 'time_warning': return '#ed8936';
      case 'time_up': return '#e53e3e';
      case 'payment_due': return '#9f7aea';
      case 'device_available': return '#38a169';
      default: return '#4299e1';
    }
  };

  const getTypeText = (type: Notification['type']) => {
    switch (type) {
      case 'time_warning': return 'تنبيه وقت';
      case 'time_up': return 'انتهاء وقت';
      case 'payment_due': return 'دفع مستحق';
      case 'device_available': return 'جهاز متاح';
      default: return 'إشعار';
    }
  };

  const handleMarkAsRead = (id: string) => {
    systemManager.markNotificationAsRead(id);
    setNotifications(systemManager.getNotifications());
  };

  const handleMarkAllAsRead = () => {
    systemManager.markAllNotificationsAsRead();
    setNotifications(systemManager.getNotifications());
  };

  const handleClearAll = () => {
    if (confirm('هل أنت متأكد من حذف جميع الإشعارات؟')) {
      // في تطبيق حقيقي، ستحتاج لإضافة وظيفة حذف الإشعارات
      // systemManager.clearAllNotifications();
      setNotifications([]);
    }
  };

  const unreadCount = notifications.filter(n => !n.isRead).length;

  return (
    <div className="notification-panel">
      <div className="notification-header">
        <h2>🔔 الإشعارات</h2>
        
        <div className="notification-actions">
          {unreadCount > 0 && (
            <button 
              className="btn btn-primary btn-sm"
              onClick={handleMarkAllAsRead}
            >
              ✓ تحديد الكل كمقروء ({unreadCount})
            </button>
          )}
          <button 
            className="btn btn-danger btn-sm"
            onClick={handleClearAll}
          >
            🗑️ حذف الكل
          </button>
        </div>
      </div>

      <div className="notification-filters">
        <button
          className={`filter-btn ${filter === 'all' ? 'active' : ''}`}
          onClick={() => setFilter('all')}
        >
          الكل ({notifications.length})
        </button>
        <button
          className={`filter-btn ${filter === 'unread' ? 'active' : ''}`}
          onClick={() => setFilter('unread')}
        >
          غير مقروء ({unreadCount})
        </button>
        <button
          className={`filter-btn ${filter === 'time_warning' ? 'active' : ''}`}
          onClick={() => setFilter('time_warning')}
        >
          تنبيهات الوقت ({notifications.filter(n => n.type === 'time_warning').length})
        </button>
        <button
          className={`filter-btn ${filter === 'time_up' ? 'active' : ''}`}
          onClick={() => setFilter('time_up')}
        >
          انتهاء الوقت ({notifications.filter(n => n.type === 'time_up').length})
        </button>
        <button
          className={`filter-btn ${filter === 'payment_due' ? 'active' : ''}`}
          onClick={() => setFilter('payment_due')}
        >
          دفع مستحق ({notifications.filter(n => n.type === 'payment_due').length})
        </button>
      </div>

      {filteredNotifications.length === 0 ? (
        <div className="empty-state">
          {filter === 'all' ? 'لا توجد إشعارات' : `لا توجد إشعارات ${getTypeText(filter as Notification['type'])}`}
        </div>
      ) : (
        <div className="notifications-list">
          {filteredNotifications.map(notification => (
            <div 
              key={notification.id} 
              className={`notification-item ${!notification.isRead ? 'unread' : ''}`}
            >
              <div className="notification-icon-wrapper">
                <span 
                  className="notification-icon"
                  style={{ color: getNotificationColor(notification.type) }}
                >
                  {getNotificationIcon(notification.type)}
                </span>
                {!notification.isRead && <div className="unread-dot"></div>}
              </div>

              <div className="notification-content">
                <div className="notification-header-content">
                  <h4>{notification.title}</h4>
                  <span 
                    className="notification-type"
                    style={{ color: getNotificationColor(notification.type) }}
                  >
                    {getTypeText(notification.type)}
                  </span>
                </div>
                
                <p>{notification.message}</p>
                
                <div className="notification-meta">
                  <span className="notification-time">
                    {format(notification.createdAt, 'HH:mm - dd/MM/yyyy', { locale: ar })}
                  </span>
                  
                  {notification.sessionId && (
                    <span className="session-link">
                      🔗 جلسة: {notification.sessionId.slice(-8)}
                    </span>
                  )}
                  
                  {notification.deviceId && (
                    <span className="device-link">
                      🎮 جهاز: {systemManager.devices.getDevice(notification.deviceId)?.name || 'غير معروف'}
                    </span>
                  )}
                  
                  {notification.playSound && (
                    <span className="sound-indicator">
                      🔊 صوت
                    </span>
                  )}
                </div>
              </div>

              <div className="notification-actions">
                {!notification.isRead && (
                  <button
                    className="btn btn-primary btn-sm"
                    onClick={() => handleMarkAsRead(notification.id)}
                    title="تحديد كمقروء"
                  >
                    ✓
                  </button>
                )}
                
                {notification.sessionId && (
                  <button
                    className="btn btn-secondary btn-sm"
                    onClick={() => {
                      // في تطبيق حقيقي، يمكن الانتقال إلى تفاصيل الجلسة
                      console.log('Navigate to session:', notification.sessionId);
                    }}
                    title="عرض الجلسة"
                  >
                    👁️
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* إحصائيات سريعة */}
      <div className="notification-stats">
        <div className="stats-grid">
          <div className="stat-item">
            <span className="stat-label">إجمالي الإشعارات</span>
            <span className="stat-value">{notifications.length}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">غير مقروء</span>
            <span className="stat-value unread">{unreadCount}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">تنبيهات الوقت</span>
            <span className="stat-value warning">{notifications.filter(n => n.type === 'time_warning').length}</span>
          </div>
          <div className="stat-item">
            <span className="stat-label">انتهاء الوقت</span>
            <span className="stat-value danger">{notifications.filter(n => n.type === 'time_up').length}</span>
          </div>
        </div>
      </div>
    </div>
  );
};
