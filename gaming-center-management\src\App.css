/* تصميم التطبيق الرئيسي */
.app {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  color: #333;
}

.app-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 1rem 2rem;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.app-header h1 {
  margin: 0 0 1rem 0;
  font-size: 2rem;
  font-weight: 700;
  color: #2d3748;
  text-align: center;
}

.app-nav {
  display: flex;
  justify-content: center;
  gap: 1rem;
  flex-wrap: wrap;
}

.nav-button {
  background: linear-gradient(135deg, #4299e1, #3182ce);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  box-shadow: 0 4px 15px rgba(66, 153, 225, 0.3);
}

.nav-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(66, 153, 225, 0.4);
}

.nav-button.active {
  background: linear-gradient(135deg, #38a169, #2f855a);
  box-shadow: 0 4px 15px rgba(56, 161, 105, 0.4);
}

.notification-badge {
  position: absolute;
  top: -8px;
  right: -8px;
  background: #e53e3e;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  font-size: 0.7rem;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.app-main {
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
}

/* بطاقات عامة */
.card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-bottom: 1.5rem;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.card-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* أزرار */
.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn-primary {
  background: linear-gradient(135deg, #4299e1, #3182ce);
  color: white;
  box-shadow: 0 4px 15px rgba(66, 153, 225, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(66, 153, 225, 0.4);
}

.btn-success {
  background: linear-gradient(135deg, #38a169, #2f855a);
  color: white;
  box-shadow: 0 4px 15px rgba(56, 161, 105, 0.3);
}

.btn-success:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(56, 161, 105, 0.4);
}

.btn-warning {
  background: linear-gradient(135deg, #ed8936, #dd6b20);
  color: white;
  box-shadow: 0 4px 15px rgba(237, 137, 54, 0.3);
}

.btn-warning:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(237, 137, 54, 0.4);
}

.btn-danger {
  background: linear-gradient(135deg, #e53e3e, #c53030);
  color: white;
  box-shadow: 0 4px 15px rgba(229, 62, 62, 0.3);
}

.btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(229, 62, 62, 0.4);
}

.btn-secondary {
  background: linear-gradient(135deg, #718096, #4a5568);
  color: white;
  box-shadow: 0 4px 15px rgba(113, 128, 150, 0.3);
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(113, 128, 150, 0.4);
}

.btn-sm {
  padding: 0.5rem 1rem;
  font-size: 0.8rem;
}

/* لوحة التحكم */
.dashboard {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 1.5rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  font-size: 2.5rem;
  opacity: 0.8;
}

.stat-content h3 {
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  color: #718096;
  font-weight: 600;
}

.stat-number {
  margin: 0;
  font-size: 2rem;
  font-weight: 700;
  color: #2d3748;
}

.stat-card.available { border-left: 4px solid #38a169; }
.stat-card.occupied { border-left: 4px solid #e53e3e; }
.stat-card.paused { border-left: 4px solid #ed8936; }
.stat-card.maintenance { border-left: 4px solid #718096; }
.stat-card.revenue { border-left: 4px solid #9f7aea; }
.stat-card.sessions { border-left: 4px solid #4299e1; }
.stat-card.duration { border-left: 4px solid #38b2ac; }

.dashboard-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 2rem;
}

@media (max-width: 1024px) {
  .dashboard-content {
    grid-template-columns: 1fr;
  }
}

/* الجلسات النشطة */
.sessions-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.session-item {
  background: rgba(247, 250, 252, 0.8);
  border-radius: 12px;
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border: 1px solid rgba(226, 232, 240, 0.8);
  transition: all 0.3s ease;
}

.session-item:hover {
  background: rgba(237, 242, 247, 0.9);
  transform: translateX(-4px);
}

.session-info {
  flex: 1;
}

.session-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.device-name {
  font-weight: 600;
  color: #2d3748;
}

.session-status {
  font-size: 0.9rem;
  font-weight: 600;
}

.session-details {
  display: flex;
  gap: 1rem;
  font-size: 0.85rem;
  color: #718096;
  flex-wrap: wrap;
}

.time-warning {
  color: #e53e3e !important;
  font-weight: 600;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.session-actions {
  display: flex;
  gap: 0.5rem;
}

/* الإشعارات */
.notifications-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.notification-item {
  background: rgba(247, 250, 252, 0.8);
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  border: 1px solid rgba(226, 232, 240, 0.8);
  transition: all 0.3s ease;
}

.notification-item.unread {
  background: rgba(66, 153, 225, 0.1);
  border-color: rgba(66, 153, 225, 0.3);
}

.notification-content h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.9rem;
  color: #2d3748;
}

.notification-content p {
  margin: 0 0 0.5rem 0;
  font-size: 0.85rem;
  color: #4a5568;
}

.notification-content small {
  color: #718096;
  font-size: 0.75rem;
}

.empty-state {
  text-align: center;
  color: #718096;
  font-style: italic;
  padding: 2rem;
}

/* شبكة الأجهزة */
.device-grid-container {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.device-grid-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.device-grid-header h2 {
  margin: 0;
  color: #2d3748;
  font-size: 1.5rem;
}

.device-stats {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.device-stats .stat {
  background: rgba(255, 255, 255, 0.9);
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.device-stats .stat.available { color: #38a169; }
.device-stats .stat.occupied { color: #e53e3e; }
.device-stats .stat.paused { color: #ed8936; }
.device-stats .stat.maintenance { color: #718096; }

.devices-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 1.5rem;
}

.device-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 2px solid transparent;
  transition: all 0.3s ease;
  position: relative;
}

.device-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.device-card.available {
  border-color: #38a169;
}

.device-card.occupied {
  border-color: #e53e3e;
}

.device-card.paused {
  border-color: #ed8936;
}

.device-card.maintenance {
  border-color: #718096;
}

.device-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.device-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.device-icon {
  font-size: 2rem;
}

.device-info h3 {
  margin: 0;
  font-size: 1.1rem;
  color: #2d3748;
}

.device-type {
  margin: 0;
  font-size: 0.85rem;
  color: #718096;
}

.device-status {
  font-size: 0.9rem;
  font-weight: 600;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.8);
}

.device-location {
  font-size: 0.85rem;
  color: #718096;
  margin-bottom: 0.75rem;
}

.device-price {
  font-size: 0.9rem;
  color: #2d3748;
  font-weight: 600;
  margin-bottom: 1rem;
}

.session-info {
  background: rgba(247, 250, 252, 0.8);
  border-radius: 8px;
  padding: 1rem;
  margin-bottom: 1rem;
  border: 1px solid rgba(226, 232, 240, 0.8);
}

.session-customer {
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 0.5rem;
}

.session-details {
  display: flex;
  gap: 1rem;
  font-size: 0.85rem;
  color: #718096;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
}

.remaining-time {
  font-size: 0.9rem;
  font-weight: 600;
  color: #4299e1;
}

.remaining-time.warning {
  color: #e53e3e;
  animation: pulse 2s infinite;
}

.device-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.device-actions .btn {
  flex: 1;
  min-width: 100px;
  justify-content: center;
}

/* النوافذ المنبثقة */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.modal {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.modal-header h3 {
  margin: 0;
  color: #2d3748;
}

.modal-close {
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #718096;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: #f7fafc;
  color: #2d3748;
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  padding: 1.5rem;
  border-top: 1px solid #e2e8f0;
}

/* النماذج */
.form-group {
  margin-bottom: 1.5rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #2d3748;
}

.form-group input,
.form-group select,
.form-group textarea {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 0.9rem;
  transition: border-color 0.3s ease;
  box-sizing: border-box;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
  outline: none;
  border-color: #4299e1;
  box-shadow: 0 0 0 3px rgba(66, 153, 225, 0.1);
}

.form-group textarea {
  resize: vertical;
  min-height: 80px;
}

/* إدارة الجلسات */
.session-manager {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.session-manager-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.session-manager-header h2 {
  margin: 0;
  color: #2d3748;
}

.session-filters {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.filter-btn {
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid #e2e8f0;
  color: #4a5568;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  font-size: 0.85rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.filter-btn:hover {
  background: #f7fafc;
  border-color: #cbd5e0;
}

.filter-btn.active {
  background: #4299e1;
  border-color: #4299e1;
  color: white;
}

.sessions-table {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.table-header {
  display: grid;
  grid-template-columns: 1fr 1fr 0.8fr 1fr 0.8fr 0.8fr 0.8fr 0.8fr 1fr;
  gap: 1rem;
  padding: 1rem;
  background: #f7fafc;
  font-weight: 600;
  color: #2d3748;
  border-bottom: 1px solid #e2e8f0;
}

.table-row {
  display: grid;
  grid-template-columns: 1fr 1fr 0.8fr 1fr 0.8fr 0.8fr 0.8fr 0.8fr 1fr;
  gap: 1rem;
  padding: 1rem;
  border-bottom: 1px solid #e2e8f0;
  transition: background-color 0.3s ease;
}

.table-row:hover {
  background: rgba(247, 250, 252, 0.8);
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  display: flex;
  align-items: center;
  font-size: 0.85rem;
  color: #4a5568;
}

.device-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.device-icon {
  font-size: 1.2rem;
}

.customer-info .customer-name {
  font-weight: 600;
  color: #2d3748;
}

.game-type {
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 600;
}

.game-type.single {
  background: rgba(56, 161, 105, 0.1);
  color: #38a169;
}

.game-type.multiplayer {
  background: rgba(66, 153, 225, 0.1);
  color: #4299e1;
}

.status-badge {
  font-weight: 600;
  font-size: 0.85rem;
}

.session-actions {
  display: flex;
  gap: 0.25rem;
}

/* إعدادات الأسعار */
.pricing-settings {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.pricing-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.pricing-header h2 {
  margin: 0;
  color: #2d3748;
}

.pricing-tabs {
  display: flex;
  gap: 0.5rem;
}

.tab-btn {
  background: rgba(255, 255, 255, 0.9);
  border: 2px solid #e2e8f0;
  color: #4a5568;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab-btn:hover {
  background: #f7fafc;
  border-color: #cbd5e0;
}

.tab-btn.active {
  background: #4299e1;
  border-color: #4299e1;
  color: white;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.section-header h3 {
  margin: 0;
  color: #2d3748;
}

.rules-list,
.offers-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.rule-card,
.offer-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.rule-card:hover,
.offer-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.rule-card.inactive,
.offer-card.inactive {
  opacity: 0.6;
  background: rgba(247, 250, 252, 0.8);
}

.rule-header,
.offer-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.rule-header h4,
.offer-header h4 {
  margin: 0;
  color: #2d3748;
  font-size: 1.1rem;
}

.rule-actions,
.offer-actions {
  display: flex;
  gap: 0.5rem;
}

.rule-details,
.offer-details {
  color: #4a5568;
}

.rule-info,
.offer-info {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  font-size: 0.85rem;
}

.rule-info span,
.offer-info span {
  background: rgba(247, 250, 252, 0.8);
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  border: 1px solid rgba(226, 232, 240, 0.8);
}

.offer-details p {
  margin: 0 0 1rem 0;
  color: #718096;
  font-style: italic;
}

.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 0.5rem;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.9rem;
  color: #4a5568;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  width: auto;
  margin: 0;
}

/* لوحة الإشعارات */
.notification-panel {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.notification-header h2 {
  margin: 0;
  color: #2d3748;
}

.notification-actions {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.notification-filters {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
  margin-bottom: 1rem;
}

.notifications-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.notification-item {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  padding: 1.5rem;
  display: flex;
  gap: 1rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
}

.notification-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.15);
}

.notification-item.unread {
  background: rgba(66, 153, 225, 0.05);
  border-color: rgba(66, 153, 225, 0.2);
}

.notification-icon-wrapper {
  position: relative;
  flex-shrink: 0;
}

.notification-icon {
  font-size: 1.5rem;
  display: block;
}

.unread-dot {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 8px;
  height: 8px;
  background: #e53e3e;
  border-radius: 50%;
  border: 2px solid white;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.5rem;
  gap: 1rem;
}

.notification-header-content h4 {
  margin: 0;
  font-size: 1rem;
  color: #2d3748;
  font-weight: 600;
}

.notification-type {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.8);
  white-space: nowrap;
}

.notification-content p {
  margin: 0 0 1rem 0;
  color: #4a5568;
  font-size: 0.9rem;
  line-height: 1.5;
}

.notification-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  font-size: 0.75rem;
  color: #718096;
}

.notification-time {
  font-weight: 600;
}

.session-link,
.device-link {
  background: rgba(247, 250, 252, 0.8);
  padding: 0.25rem 0.5rem;
  border-radius: 8px;
  border: 1px solid rgba(226, 232, 240, 0.8);
}

.sound-indicator {
  color: #ed8936;
  font-weight: 600;
}

.notification-actions {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex-shrink: 0;
}

.notification-stats {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 1.5rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1rem;
}

.stat-item {
  text-align: center;
  padding: 1rem;
  background: rgba(247, 250, 252, 0.8);
  border-radius: 8px;
  border: 1px solid rgba(226, 232, 240, 0.8);
}

.stat-label {
  display: block;
  font-size: 0.8rem;
  color: #718096;
  margin-bottom: 0.5rem;
}

.stat-value {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #2d3748;
}

.stat-value.unread {
  color: #4299e1;
}

.stat-value.warning {
  color: #ed8936;
}

.stat-value.danger {
  color: #e53e3e;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
  .app-nav {
    justify-content: flex-start;
  }

  .nav-button {
    font-size: 0.8rem;
    padding: 0.5rem 1rem;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .devices-grid {
    grid-template-columns: 1fr;
  }

  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .table-cell {
    padding: 0.5rem 0;
    border-bottom: 1px solid #e2e8f0;
  }

  .table-cell:last-child {
    border-bottom: none;
  }

  .modal {
    width: 95%;
    margin: 1rem;
  }

  .notification-item {
    flex-direction: column;
    gap: 1rem;
  }

  .notification-actions {
    flex-direction: row;
    justify-content: flex-end;
  }
}

/* تحسينات إضافية */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

.loading {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.loading::after {
  content: '';
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* تحسينات الطباعة */
@media print {
  .app-header,
  .notification-actions,
  .device-actions,
  .session-actions,
  .modal-overlay {
    display: none !important;
  }

  .app {
    background: white !important;
  }

  .card,
  .device-card,
  .notification-item {
    background: white !important;
    box-shadow: none !important;
    border: 1px solid #ccc !important;
  }
}
