import { Device, Session, Customer, SystemEvent } from '../types';
import { TimeManager } from './TimeManager';
import { PricingManager } from './PricingManager';

export class DeviceManager {
  private devices: Map<string, Device> = new Map();
  private timeManager: TimeManager;
  private pricingManager: PricingManager;
  private eventListeners: ((event: SystemEvent) => void)[] = [];

  constructor(timeManager: TimeManager, pricingManager: PricingManager) {
    this.timeManager = timeManager;
    this.pricingManager = pricingManager;
    this.initializeDefaultDevices();
  }

  // إضافة مستمع للأحداث
  addEventListener(listener: (event: SystemEvent) => void) {
    this.eventListeners.push(listener);
  }

  // إزالة مستمع الأحداث
  removeEventListener(listener: (event: SystemEvent) => void) {
    const index = this.eventListeners.indexOf(listener);
    if (index > -1) {
      this.eventListeners.splice(index, 1);
    }
  }

  // إرسال حدث للمستمعين
  private emitEvent(event: SystemEvent) {
    this.eventListeners.forEach(listener => listener(event));
  }

  // إضافة جهاز جديد
  addDevice(deviceData: Omit<Device, 'id' | 'createdAt' | 'updatedAt' | 'status'>): Device {
    const device: Device = {
      ...deviceData,
      id: this.generateId(),
      status: 'available',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    this.devices.set(device.id, device);
    return device;
  }

  // تحديث جهاز
  updateDevice(id: string, updates: Partial<Device>): Device | null {
    const device = this.devices.get(id);
    if (!device) return null;

    const oldStatus = device.status;
    const updatedDevice: Device = {
      ...device,
      ...updates,
      updatedAt: new Date()
    };

    this.devices.set(id, updatedDevice);

    // إرسال حدث تغيير الحالة إذا تغيرت
    if (oldStatus !== updatedDevice.status) {
      this.emitEvent({
        type: 'DEVICE_STATUS_CHANGED',
        payload: { deviceId: id, oldStatus, newStatus: updatedDevice.status }
      });
    }

    return updatedDevice;
  }

  // حذف جهاز
  deleteDevice(id: string): boolean {
    const device = this.devices.get(id);
    if (!device) return false;

    // التأكد من عدم وجود جلسة نشطة
    if (device.status === 'occupied' || device.currentSession) {
      throw new Error('لا يمكن حذف جهاز يحتوي على جلسة نشطة');
    }

    this.devices.delete(id);
    return true;
  }

  // بدء جلسة على جهاز
  startSession(deviceId: string, sessionData: {
    customerName: string;
    customerId?: string;
    gameType: 'single' | 'multiplayer';
    plannedDuration?: number;
    notes?: string;
  }): Session | null {
    const device = this.devices.get(deviceId);
    if (!device || device.status !== 'available') {
      return null;
    }

    const startTime = new Date();
    const pricing = this.pricingManager.calculatePrice(
      device,
      sessionData.gameType,
      startTime,
      sessionData.plannedDuration || 60
    );

    const session: Session = {
      id: this.generateId(),
      deviceId,
      customerId: sessionData.customerId,
      customerName: sessionData.customerName,
      startTime,
      plannedDuration: sessionData.plannedDuration,
      gameType: sessionData.gameType,
      pricePerHour: pricing.appliedRule?.pricePerHour || device.pricePerHour,
      totalCost: 0,
      status: 'active',
      notes: sessionData.notes,
      createdAt: startTime,
      updatedAt: startTime
    };

    // تحديث حالة الجهاز
    this.updateDevice(deviceId, {
      status: 'occupied',
      currentSession: session
    });

    // بدء الجلسة في مدير الوقت
    this.timeManager.startSession(session);

    return session;
  }

  // إيقاف الجلسة مؤقتاً
  pauseSession(deviceId: string): Session | null {
    const device = this.devices.get(deviceId);
    if (!device || !device.currentSession || device.status !== 'occupied') {
      return null;
    }

    const pausedSession = this.timeManager.pauseSession(device.currentSession.id);
    if (!pausedSession) return null;

    // تحديث حالة الجهاز
    this.updateDevice(deviceId, {
      status: 'paused',
      currentSession: pausedSession
    });

    return pausedSession;
  }

  // استئناف الجلسة
  resumeSession(deviceId: string): Session | null {
    const device = this.devices.get(deviceId);
    if (!device || !device.currentSession || device.status !== 'paused') {
      return null;
    }

    const resumedSession = this.timeManager.resumeSession(device.currentSession.id);
    if (!resumedSession) return null;

    // تحديث حالة الجهاز
    this.updateDevice(deviceId, {
      status: 'occupied',
      currentSession: resumedSession
    });

    return resumedSession;
  }

  // إنهاء الجلسة
  endSession(deviceId: string): Session | null {
    const device = this.devices.get(deviceId);
    if (!device || !device.currentSession) {
      return null;
    }

    const completedSession = this.timeManager.endSession(device.currentSession.id);
    if (!completedSession) return null;

    // تحديث حالة الجهاز
    this.updateDevice(deviceId, {
      status: 'available',
      currentSession: undefined
    });

    return completedSession;
  }

  // نقل الجلسة من جهاز إلى آخر
  transferSession(fromDeviceId: string, toDeviceId: string): boolean {
    const fromDevice = this.devices.get(fromDeviceId);
    const toDevice = this.devices.get(toDeviceId);

    if (!fromDevice || !toDevice || !fromDevice.currentSession || toDevice.status !== 'available') {
      return false;
    }

    const session = fromDevice.currentSession;

    // إيقاف الجلسة مؤقتاً على الجهاز الأول
    const pausedSession = this.timeManager.pauseSession(session.id);
    if (!pausedSession) return false;

    // تحديث معرف الجهاز في الجلسة
    const transferredSession: Session = {
      ...pausedSession,
      deviceId: toDeviceId,
      updatedAt: new Date()
    };

    // تحديث الأجهزة
    this.updateDevice(fromDeviceId, {
      status: 'available',
      currentSession: undefined
    });

    this.updateDevice(toDeviceId, {
      status: 'occupied',
      currentSession: transferredSession
    });

    // استئناف الجلسة على الجهاز الجديد
    this.timeManager.resumeSession(session.id);

    return true;
  }

  // تغيير حالة الجهاز للصيانة
  setDeviceForMaintenance(deviceId: string, notes?: string): boolean {
    const device = this.devices.get(deviceId);
    if (!device) return false;

    // التأكد من عدم وجود جلسة نشطة
    if (device.status === 'occupied' || device.currentSession) {
      return false;
    }

    this.updateDevice(deviceId, {
      status: 'maintenance',
      notes
    });

    return true;
  }

  // إعادة الجهاز من الصيانة
  setDeviceAvailable(deviceId: string): boolean {
    const device = this.devices.get(deviceId);
    if (!device || device.status !== 'maintenance') {
      return false;
    }

    this.updateDevice(deviceId, {
      status: 'available',
      notes: undefined
    });

    return true;
  }

  // الحصول على جهاز
  getDevice(id: string): Device | null {
    return this.devices.get(id) || null;
  }

  // الحصول على جميع الأجهزة
  getAllDevices(): Device[] {
    return Array.from(this.devices.values());
  }

  // الحصول على الأجهزة المتاحة
  getAvailableDevices(): Device[] {
    return Array.from(this.devices.values()).filter(device => device.status === 'available');
  }

  // الحصول على الأجهزة المشغولة
  getOccupiedDevices(): Device[] {
    return Array.from(this.devices.values()).filter(device => device.status === 'occupied');
  }

  // الحصول على الأجهزة المتوقفة مؤقتاً
  getPausedDevices(): Device[] {
    return Array.from(this.devices.values()).filter(device => device.status === 'paused');
  }

  // الحصول على الأجهزة في الصيانة
  getMaintenanceDevices(): Device[] {
    return Array.from(this.devices.values()).filter(device => device.status === 'maintenance');
  }

  // البحث عن الأجهزة
  searchDevices(query: string): Device[] {
    const lowerQuery = query.toLowerCase();
    return Array.from(this.devices.values()).filter(device =>
      device.name.toLowerCase().includes(lowerQuery) ||
      device.type.toLowerCase().includes(lowerQuery) ||
      device.location?.toLowerCase().includes(lowerQuery)
    );
  }

  // الحصول على إحصائيات الأجهزة
  getDeviceStats(): {
    total: number;
    available: number;
    occupied: number;
    paused: number;
    maintenance: number;
    utilizationRate: number;
  } {
    const devices = Array.from(this.devices.values());
    const total = devices.length;
    const available = devices.filter(d => d.status === 'available').length;
    const occupied = devices.filter(d => d.status === 'occupied').length;
    const paused = devices.filter(d => d.status === 'paused').length;
    const maintenance = devices.filter(d => d.status === 'maintenance').length;
    const utilizationRate = total > 0 ? ((occupied + paused) / total) * 100 : 0;

    return {
      total,
      available,
      occupied,
      paused,
      maintenance,
      utilizationRate: Math.round(utilizationRate * 100) / 100
    };
  }

  // تهيئة الأجهزة الافتراضية
  private initializeDefaultDevices(): void {
    const defaultDevices = [
      { name: 'PS5 - 1', type: 'PS5' as const, pricePerHour: 25, location: 'الصالة الرئيسية' },
      { name: 'PS5 - 2', type: 'PS5' as const, pricePerHour: 25, location: 'الصالة الرئيسية' },
      { name: 'PS4 - 1', type: 'PS4' as const, pricePerHour: 20, location: 'الصالة الرئيسية' },
      { name: 'PS4 - 2', type: 'PS4' as const, pricePerHour: 20, location: 'الصالة الرئيسية' },
      { name: 'Xbox Series X - 1', type: 'Xbox' as const, pricePerHour: 25, location: 'الصالة الثانوية' },
      { name: 'PC Gaming - 1', type: 'PC' as const, pricePerHour: 30, location: 'منطقة الكمبيوتر' },
      { name: 'PC Gaming - 2', type: 'PC' as const, pricePerHour: 30, location: 'منطقة الكمبيوتر' },
      { name: 'Nintendo Switch - 1', type: 'Nintendo' as const, pricePerHour: 15, location: 'منطقة العائلة' }
    ];

    defaultDevices.forEach(deviceData => {
      this.addDevice(deviceData);
    });
  }

  // توليد معرف فريد
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  // تنظيف الموارد
  cleanup(): void {
    this.devices.clear();
    this.eventListeners.length = 0;
  }
}
