import { TimeManager } from './TimeManager';
import { PricingManager } from './PricingManager';
import { DeviceManager } from './DeviceManager';
import { 
  Device, 
  Session, 
  Customer, 
  SystemEvent, 
  Notification, 
  DashboardStats, 
  SystemSettings 
} from '../types';

export class SystemManager {
  private timeManager: TimeManager;
  private pricingManager: PricingManager;
  private deviceManager: DeviceManager;
  private notifications: Notification[] = [];
  private customers: Map<string, Customer> = new Map();
  private settings: SystemSettings;
  private eventListeners: ((event: SystemEvent) => void)[] = [];

  constructor() {
    // تهيئة الإعدادات الافتراضية
    this.settings = {
      warningTime: 10,
      soundEnabled: true,
      autoCalculatePricing: true,
      defaultSessionDuration: 60,
      currencySymbol: 'ريال',
      businessHours: {
        open: '10:00',
        close: '02:00'
      },
      timezone: 'Asia/Riyadh'
    };

    // تهيئة المدراء
    this.timeManager = new TimeManager(this.settings.warningTime);
    this.pricingManager = new PricingManager();
    this.deviceManager = new DeviceManager(this.timeManager, this.pricingManager);

    // ربط الأحداث
    this.setupEventListeners();
  }

  // إعداد مستمعي الأحداث
  private setupEventListeners(): void {
    // أحداث مدير الوقت
    this.timeManager.addEventListener((event) => {
      this.handleSystemEvent(event);
    });

    // أحداث مدير الأجهزة
    this.deviceManager.addEventListener((event) => {
      this.handleSystemEvent(event);
    });
  }

  // معالجة أحداث النظام
  private handleSystemEvent(event: SystemEvent): void {
    switch (event.type) {
      case 'TIME_WARNING':
        this.createNotification({
          type: 'time_warning',
          title: 'تنبيه انتهاء الوقت',
          message: `باقي ${event.payload.remainingMinutes} دقائق على انتهاء الجلسة`,
          sessionId: event.payload.sessionId,
          playSound: this.settings.soundEnabled
        });
        break;

      case 'TIME_UP':
        this.createNotification({
          type: 'time_up',
          title: 'انتهى الوقت',
          message: 'انتهى الوقت المحدد للجلسة',
          sessionId: event.payload.sessionId,
          playSound: this.settings.soundEnabled
        });
        break;

      case 'SESSION_STARTED':
        this.updateCustomerStats(event.payload.customerId, 'session_started');
        break;

      case 'SESSION_ENDED':
        this.updateCustomerStats(event.payload.customerId, 'session_ended', event.payload);
        break;

      case 'PAYMENT_CALCULATED':
        this.createNotification({
          type: 'payment_due',
          title: 'حساب الدفع',
          message: `المبلغ المستحق: ${event.payload.amount} ${this.settings.currencySymbol}`,
          sessionId: event.payload.sessionId,
          playSound: false
        });
        break;
    }

    // إعادة إرسال الحدث للمستمعين الخارجيين
    this.emitEvent(event);
  }

  // إضافة مستمع للأحداث
  addEventListener(listener: (event: SystemEvent) => void): void {
    this.eventListeners.push(listener);
  }

  // إزالة مستمع الأحداث
  removeEventListener(listener: (event: SystemEvent) => void): void {
    const index = this.eventListeners.indexOf(listener);
    if (index > -1) {
      this.eventListeners.splice(index, 1);
    }
  }

  // إرسال حدث للمستمعين
  private emitEvent(event: SystemEvent): void {
    this.eventListeners.forEach(listener => listener(event));
  }

  // إنشاء إشعار جديد
  private createNotification(notificationData: Omit<Notification, 'id' | 'isRead' | 'createdAt'>): void {
    const notification: Notification = {
      ...notificationData,
      id: this.generateId(),
      isRead: false,
      createdAt: new Date()
    };

    this.notifications.unshift(notification);

    // الاحتفاظ بآخر 100 إشعار فقط
    if (this.notifications.length > 100) {
      this.notifications = this.notifications.slice(0, 100);
    }
  }

  // تحديث إحصائيات العميل
  private updateCustomerStats(customerId?: string, action?: string, session?: Session): void {
    if (!customerId) return;

    let customer = this.customers.get(customerId);
    if (!customer) {
      customer = {
        id: customerId,
        name: session?.customerName || 'عميل جديد',
        totalPlayTime: 0,
        totalSpent: 0,
        membershipLevel: 'regular',
        createdAt: new Date()
      };
    }

    if (action === 'session_ended' && session) {
      customer.totalPlayTime += session.actualDuration || 0;
      customer.totalSpent += session.totalCost;
      customer.lastVisit = new Date();

      // تحديث مستوى العضوية
      customer.membershipLevel = this.calculateMembershipLevel(customer.totalSpent);
    }

    this.customers.set(customerId, customer);
  }

  // حساب مستوى العضوية
  private calculateMembershipLevel(totalSpent: number): Customer['membershipLevel'] {
    if (totalSpent >= 1000) return 'platinum';
    if (totalSpent >= 500) return 'gold';
    if (totalSpent >= 200) return 'silver';
    return 'regular';
  }

  // الحصول على إحصائيات لوحة التحكم
  getDashboardStats(): DashboardStats {
    const deviceStats = this.deviceManager.getDeviceStats();
    const activeSessions = this.timeManager.getActiveSessions();
    
    // حساب إيرادات اليوم
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const todaySessions = Array.from(this.customers.values())
      .filter(customer => customer.lastVisit && customer.lastVisit >= today);

    const todayRevenue = todaySessions.reduce((total, customer) => total + customer.totalSpent, 0);

    // حساب متوسط مدة الجلسة
    const completedSessions = activeSessions.filter(s => s.status === 'completed');
    const averageSessionDuration = completedSessions.length > 0
      ? completedSessions.reduce((total, session) => total + (session.actualDuration || 0), 0) / completedSessions.length
      : 0;

    // نوع الجهاز الأكثر شعبية
    const deviceTypes = this.deviceManager.getAllDevices().map(d => d.type);
    const popularDeviceType = this.getMostFrequent(deviceTypes) || 'PS4';

    return {
      totalDevices: deviceStats.total,
      availableDevices: deviceStats.available,
      occupiedDevices: deviceStats.occupied,
      pausedDevices: deviceStats.paused,
      maintenanceDevices: deviceStats.maintenance,
      todayRevenue,
      todaySessions: todaySessions.length,
      averageSessionDuration: Math.round(averageSessionDuration),
      popularDeviceType
    };
  }

  // العثور على العنصر الأكثر تكراراً
  private getMostFrequent<T>(arr: T[]): T | null {
    if (arr.length === 0) return null;
    
    const frequency: { [key: string]: number } = {};
    let maxCount = 0;
    let mostFrequent: T = arr[0];

    arr.forEach(item => {
      const key = String(item);
      frequency[key] = (frequency[key] || 0) + 1;
      if (frequency[key] > maxCount) {
        maxCount = frequency[key];
        mostFrequent = item;
      }
    });

    return mostFrequent;
  }

  // الحصول على الإشعارات
  getNotifications(limit?: number): Notification[] {
    return limit ? this.notifications.slice(0, limit) : [...this.notifications];
  }

  // الحصول على الإشعارات غير المقروءة
  getUnreadNotifications(): Notification[] {
    return this.notifications.filter(n => !n.isRead);
  }

  // تحديد الإشعار كمقروء
  markNotificationAsRead(id: string): boolean {
    const notification = this.notifications.find(n => n.id === id);
    if (notification) {
      notification.isRead = true;
      return true;
    }
    return false;
  }

  // تحديد جميع الإشعارات كمقروءة
  markAllNotificationsAsRead(): void {
    this.notifications.forEach(n => n.isRead = true);
  }

  // الحصول على العملاء
  getCustomers(): Customer[] {
    return Array.from(this.customers.values());
  }

  // إضافة عميل جديد
  addCustomer(customerData: Omit<Customer, 'id' | 'totalPlayTime' | 'totalSpent' | 'membershipLevel' | 'createdAt'>): Customer {
    const customer: Customer = {
      ...customerData,
      id: this.generateId(),
      totalPlayTime: 0,
      totalSpent: 0,
      membershipLevel: 'regular',
      createdAt: new Date()
    };

    this.customers.set(customer.id, customer);
    return customer;
  }

  // تحديث بيانات العميل
  updateCustomer(id: string, updates: Partial<Customer>): Customer | null {
    const customer = this.customers.get(id);
    if (!customer) return null;

    const updatedCustomer = { ...customer, ...updates };
    this.customers.set(id, updatedCustomer);
    return updatedCustomer;
  }

  // الحصول على الإعدادات
  getSettings(): SystemSettings {
    return { ...this.settings };
  }

  // تحديث الإعدادات
  updateSettings(updates: Partial<SystemSettings>): SystemSettings {
    this.settings = { ...this.settings, ...updates };
    
    // تحديث إعدادات مدير الوقت إذا تغير وقت التنبيه
    if (updates.warningTime) {
      this.timeManager = new TimeManager(updates.warningTime);
    }

    return this.settings;
  }

  // الوصول إلى المدراء الفرعيين
  get devices() {
    return this.deviceManager;
  }

  get pricing() {
    return this.pricingManager;
  }

  get time() {
    return this.timeManager;
  }

  // توليد معرف فريد
  private generateId(): string {
    return Date.now().toString(36) + Math.random().toString(36).substr(2);
  }

  // تنظيف الموارد
  cleanup(): void {
    this.timeManager.cleanup();
    this.deviceManager.cleanup();
    this.notifications.length = 0;
    this.customers.clear();
    this.eventListeners.length = 0;
  }
}
